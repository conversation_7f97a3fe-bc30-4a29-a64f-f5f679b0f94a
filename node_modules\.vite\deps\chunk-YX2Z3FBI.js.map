{"version": 3, "sources": ["../../highlight.js/lib/languages/qml.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: QML\nRequires: javascript.js, xml.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Syntax highlighting for the Qt Quick QML scripting language, based mostly off\n             the JavaScript parser.\nWebsite: https://doc.qt.io/qt-5/qmlapplications.html\nCategory: scripting\n*/\n\nfunction qml(hljs) {\n  const KEYWORDS = {\n    keyword:\n      'in of on if for while finally var new function do return void else break catch ' +\n      'instanceof with throw case default try this switch continue typeof delete ' +\n      'let yield const export super debugger as async await import',\n    literal:\n      'true false null undefined NaN Infinity',\n    built_in:\n      'eval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent ' +\n      'encodeURI encodeURIComponent escape unescape Object Function Boolean Error ' +\n      'EvalError InternalError RangeError ReferenceError StopIteration SyntaxError ' +\n      'TypeError URIError Number Math Date String RegExp Array Float32Array ' +\n      'Float64Array Int16Array Int32Array Int8Array Uint16Array Uint32Array ' +\n      'Uint8Array Uint8ClampedArray ArrayBuffer DataView JSON Intl arguments require ' +\n      'module console window document Symbol Set Map WeakSet WeakMap Proxy Reflect ' +\n      'Behavior bool color coordinate date double enumeration font geocircle georectangle ' +\n      'geoshape int list matrix4x4 parent point quaternion real rect ' +\n      'size string url variant vector2d vector3d vector4d ' +\n      'Promise'\n  };\n\n  const QML_IDENT_RE = '[a-zA-Z_][a-zA-Z0-9\\\\._]*';\n\n  // Isolate property statements. Ends at a :, =, ;, ,, a comment or end of line.\n  // Use property class.\n  const PROPERTY = {\n    className: 'keyword',\n    begin: '\\\\bproperty\\\\b',\n    starts: {\n      className: 'string',\n      end: '(:|=|;|,|//|/\\\\*|$)',\n      returnEnd: true\n    }\n  };\n\n  // Isolate signal statements. Ends at a ) a comment or end of line.\n  // Use property class.\n  const SIGNAL = {\n    className: 'keyword',\n    begin: '\\\\bsignal\\\\b',\n    starts: {\n      className: 'string',\n      end: '(\\\\(|:|=|;|,|//|/\\\\*|$)',\n      returnEnd: true\n    }\n  };\n\n  // id: is special in QML. When we see id: we want to mark the id: as attribute and\n  // emphasize the token following.\n  const ID_ID = {\n    className: 'attribute',\n    begin: '\\\\bid\\\\s*:',\n    starts: {\n      className: 'string',\n      end: QML_IDENT_RE,\n      returnEnd: false\n    }\n  };\n\n  // Find QML object attribute. An attribute is a QML identifier followed by :.\n  // Unfortunately it's hard to know where it ends, as it may contain scalars,\n  // objects, object definitions, or javascript. The true end is either when the parent\n  // ends or the next attribute is detected.\n  const QML_ATTRIBUTE = {\n    begin: QML_IDENT_RE + '\\\\s*:',\n    returnBegin: true,\n    contains: [\n      {\n        className: 'attribute',\n        begin: QML_IDENT_RE,\n        end: '\\\\s*:',\n        excludeEnd: true,\n        relevance: 0\n      }\n    ],\n    relevance: 0\n  };\n\n  // Find QML object. A QML object is a QML identifier followed by { and ends at the matching }.\n  // All we really care about is finding IDENT followed by { and just mark up the IDENT and ignore the {.\n  const QML_OBJECT = {\n    begin: concat(QML_IDENT_RE, /\\s*\\{/),\n    end: /\\{/,\n    returnBegin: true,\n    relevance: 0,\n    contains: [\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: QML_IDENT_RE\n      })\n    ]\n  };\n\n  return {\n    name: 'QML',\n    aliases: [ 'qt' ],\n    case_insensitive: false,\n    keywords: KEYWORDS,\n    contains: [\n      {\n        className: 'meta',\n        begin: /^\\s*['\"]use (strict|asm)['\"]/\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      { // template string\n        className: 'string',\n        begin: '`',\n        end: '`',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          {\n            className: 'subst',\n            begin: '\\\\$\\\\{',\n            end: '\\\\}'\n          }\n        ]\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'number',\n        variants: [\n          {\n            begin: '\\\\b(0[bB][01]+)'\n          },\n          {\n            begin: '\\\\b(0[oO][0-7]+)'\n          },\n          {\n            begin: hljs.C_NUMBER_RE\n          }\n        ],\n        relevance: 0\n      },\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        contains: [\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          hljs.REGEXP_MODE,\n          { // E4X / JSX\n            begin: /</,\n            end: />\\s*[);\\]]/,\n            relevance: 0,\n            subLanguage: 'xml'\n          }\n        ],\n        relevance: 0\n      },\n      SIGNAL,\n      PROPERTY,\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: /[A-Za-z$_][0-9A-Za-z$_]*/\n          }),\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            contains: [\n              hljs.C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          }\n        ],\n        illegal: /\\[|%/\n      },\n      {\n        // hack: prevents detection of keywords after dots\n        begin: '\\\\.' + hljs.IDENT_RE,\n        relevance: 0\n      },\n      ID_ID,\n      QML_ATTRIBUTE,\n      QML_OBJECT\n    ],\n    illegal: /#/\n  };\n}\n\nmodule.exports = qml;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAYA,aAAS,IAAI,MAAM;AACjB,YAAM,WAAW;AAAA,QACf,SACE;AAAA,QAGF,SACE;AAAA,QACF,UACE;AAAA,MAWJ;AAEA,YAAM,eAAe;AAIrB,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,KAAK;AAAA,UACL,WAAW;AAAA,QACb;AAAA,MACF;AAIA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,KAAK;AAAA,UACL,WAAW;AAAA,QACb;AAAA,MACF;AAIA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,KAAK;AAAA,UACL,WAAW;AAAA,QACb;AAAA,MACF;AAMA,YAAM,gBAAgB;AAAA,QACpB,OAAO,eAAe;AAAA,QACtB,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AAIA,YAAM,aAAa;AAAA,QACjB,OAAO,OAAO,cAAc,OAAO;AAAA,QACnC,KAAK;AAAA,QACL,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK,QAAQ,KAAK,YAAY;AAAA,YAC5B,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO,KAAK;AAAA,cACd;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YACE,OAAO,MAAM,KAAK,iBAAiB;AAAA,YACnC,UAAU;AAAA,YACV,UAAU;AAAA,cACR,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL;AAAA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,WAAW;AAAA,gBACX,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,cACR,KAAK,QAAQ,KAAK,YAAY;AAAA,gBAC5B,OAAO;AAAA,cACT,CAAC;AAAA,cACD;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,UAAU;AAAA,kBACR,KAAK;AAAA,kBACL,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,YACF;AAAA,YACA,SAAS;AAAA,UACX;AAAA,UACA;AAAA;AAAA,YAEE,OAAO,QAAQ,KAAK;AAAA,YACpB,WAAW;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}