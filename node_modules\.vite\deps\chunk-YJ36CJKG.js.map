{"version": 3, "sources": ["../../highlight.js/lib/languages/elixir.js"], "sourcesContent": ["/*\nLanguage: Elixir\nAuthor: <PERSON> <<EMAIL>>\nDescription: language definition for Elixir source code files (.ex and .exs).  Based on ruby language support.\nCategory: functional\nWebsite: https://elixir-lang.org\n*/\n\n/** @type LanguageFn */\nfunction elixir(hljs) {\n  const ELIXIR_IDENT_RE = '[a-zA-Z_][a-zA-Z0-9_.]*(!|\\\\?)?';\n  const ELIXIR_METHOD_RE = '[a-zA-Z_]\\\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\\\*\\\\*|[-/+%^&*~`|]|\\\\[\\\\]=?';\n  const ELIXIR_KEYWORDS = {\n    $pattern: ELIXIR_IDENT_RE,\n    keyword: 'and false then defined module in return redo retry end for true self when ' +\n    'next until do begin unless nil break not case cond alias while ensure or ' +\n    'include use alias fn quote require import with|0'\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: ELIXIR_KEYWORDS\n  };\n  const NUMBER = {\n    className: 'number',\n    begin: '(\\\\b0o[0-7_]+)|(\\\\b0b[01_]+)|(\\\\b0x[0-9a-fA-F_]+)|(-?\\\\b[1-9][0-9_]*(\\\\.[0-9_]+([eE][-+]?[0-9]+)?)?)',\n    relevance: 0\n  };\n  const SIGIL_DELIMITERS = '[/|([{<\"\\']';\n  const LOWERCASE_SIGIL = {\n    className: 'string',\n    begin: '~[a-z]' + '(?=' + SIGIL_DELIMITERS + ')',\n    contains: [\n      {\n        endsParent: true,\n        contains: [\n          {\n            contains: [\n              hljs.BACKSLASH_ESCAPE,\n              SUBST\n            ],\n            variants: [\n              {\n                begin: /\"/,\n                end: /\"/\n              },\n              {\n                begin: /'/,\n                end: /'/\n              },\n              {\n                begin: /\\//,\n                end: /\\//\n              },\n              {\n                begin: /\\|/,\n                end: /\\|/\n              },\n              {\n                begin: /\\(/,\n                end: /\\)/\n              },\n              {\n                begin: /\\[/,\n                end: /\\]/\n              },\n              {\n                begin: /\\{/,\n                end: /\\}/\n              },\n              {\n                begin: /</,\n                end: />/\n              }\n            ]\n          }\n        ]\n      }\n    ]\n  };\n\n  const UPCASE_SIGIL = {\n    className: 'string',\n    begin: '~[A-Z]' + '(?=' + SIGIL_DELIMITERS + ')',\n    contains: [\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\\//,\n        end: /\\//\n      },\n      {\n        begin: /\\|/,\n        end: /\\|/\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/\n      },\n      {\n        begin: /\\[/,\n        end: /\\]/\n      },\n      {\n        begin: /\\{/,\n        end: /\\}/\n      },\n      {\n        begin: /</,\n        end: />/\n      }\n    ]\n  };\n\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ],\n    variants: [\n      {\n        begin: /\"\"\"/,\n        end: /\"\"\"/\n      },\n      {\n        begin: /'''/,\n        end: /'''/\n      },\n      {\n        begin: /~S\"\"\"/,\n        end: /\"\"\"/,\n        contains: [] // override default\n      },\n      {\n        begin: /~S\"/,\n        end: /\"/,\n        contains: [] // override default\n      },\n      {\n        begin: /~S'''/,\n        end: /'''/,\n        contains: [] // override default\n      },\n      {\n        begin: /~S'/,\n        end: /'/,\n        contains: [] // override default\n      },\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      }\n    ]\n  };\n  const FUNCTION = {\n    className: 'function',\n    beginKeywords: 'def defp defmacro',\n    end: /\\B\\b/, // the mode is ended by the title\n    contains: [\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: ELIXIR_IDENT_RE,\n        endsParent: true\n      })\n    ]\n  };\n  const CLASS = hljs.inherit(FUNCTION, {\n    className: 'class',\n    beginKeywords: 'defimpl defmodule defprotocol defrecord',\n    end: /\\bdo\\b|$|;/\n  });\n  const ELIXIR_DEFAULT_CONTAINS = [\n    STRING,\n    UPCASE_SIGIL,\n    LOWERCASE_SIGIL,\n    hljs.HASH_COMMENT_MODE,\n    CLASS,\n    FUNCTION,\n    {\n      begin: '::'\n    },\n    {\n      className: 'symbol',\n      begin: ':(?![\\\\s:])',\n      contains: [\n        STRING,\n        {\n          begin: ELIXIR_METHOD_RE\n        }\n      ],\n      relevance: 0\n    },\n    {\n      className: 'symbol',\n      begin: ELIXIR_IDENT_RE + ':(?!:)',\n      relevance: 0\n    },\n    NUMBER,\n    {\n      className: 'variable',\n      begin: '(\\\\$\\\\W)|((\\\\$|@@?)(\\\\w+))'\n    },\n    {\n      begin: '->'\n    },\n    { // regexp container\n      begin: '(' + hljs.RE_STARTERS_RE + ')\\\\s*',\n      contains: [\n        hljs.HASH_COMMENT_MODE,\n        {\n          // to prevent false regex triggers for the division function:\n          // /:\n          begin: /\\/: (?=\\d+\\s*[,\\]])/,\n          relevance: 0,\n          contains: [NUMBER]\n        },\n        {\n          className: 'regexp',\n          illegal: '\\\\n',\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ],\n          variants: [\n            {\n              begin: '/',\n              end: '/[a-z]*'\n            },\n            {\n              begin: '%r\\\\[',\n              end: '\\\\][a-z]*'\n            }\n          ]\n        }\n      ],\n      relevance: 0\n    }\n  ];\n  SUBST.contains = ELIXIR_DEFAULT_CONTAINS;\n\n  return {\n    name: 'Elixir',\n    keywords: ELIXIR_KEYWORDS,\n    contains: ELIXIR_DEFAULT_CONTAINS\n  };\n}\n\nmodule.exports = elixir;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,MAAM;AACpB,YAAM,kBAAkB;AACxB,YAAM,mBAAmB;AACzB,YAAM,kBAAkB;AAAA,QACtB,UAAU;AAAA,QACV,SAAS;AAAA,MAGX;AACA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,mBAAmB;AACzB,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO,cAAmB,mBAAmB;AAAA,QAC7C,UAAU;AAAA,UACR;AAAA,YACE,YAAY;AAAA,YACZ,UAAU;AAAA,cACR;AAAA,gBACE,UAAU;AAAA,kBACR,KAAK;AAAA,kBACL;AAAA,gBACF;AAAA,gBACA,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,kBACP;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,kBACP;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,kBACP;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,kBACP;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,kBACP;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,kBACP;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,kBACP;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,kBACP;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO,cAAmB,mBAAmB;AAAA,QAC7C,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC;AAAA;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC;AAAA;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC;AAAA;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC;AAAA;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA;AAAA,QACL,UAAU;AAAA,UACR,KAAK,QAAQ,KAAK,YAAY;AAAA,YAC5B,OAAO;AAAA,YACP,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,QAAQ,KAAK,QAAQ,UAAU;AAAA,QACnC,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,MACP,CAAC;AACD,YAAM,0BAA0B;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,UACE,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,UAAU;AAAA,YACR;AAAA,YACA;AAAA,cACE,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,WAAW;AAAA,QACb;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,kBAAkB;AAAA,UACzB,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,OAAO;AAAA,QACT;AAAA,QACA;AAAA;AAAA,UACE,OAAO,MAAM,KAAK,iBAAiB;AAAA,UACnC,UAAU;AAAA,YACR,KAAK;AAAA,YACL;AAAA;AAAA;AAAA,cAGE,OAAO;AAAA,cACP,WAAW;AAAA,cACX,UAAU,CAAC,MAAM;AAAA,YACnB;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,SAAS;AAAA,cACT,UAAU;AAAA,gBACR,KAAK;AAAA,gBACL;AAAA,cACF;AAAA,cACA,UAAU;AAAA,gBACR;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,gBACA;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,WAAW;AAAA,QACb;AAAA,MACF;AACA,YAAM,WAAW;AAEjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}