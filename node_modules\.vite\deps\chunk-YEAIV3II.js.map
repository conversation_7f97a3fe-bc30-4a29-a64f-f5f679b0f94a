{"version": 3, "sources": ["../../highlight.js/lib/languages/parser3.js"], "sourcesContent": ["/*\nLanguage: Parser3\nRequires: xml.js\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.parser.ru/en/\nCategory: template\n*/\n\nfunction parser3(hljs) {\n  const CURLY_SUBCOMMENT = hljs.COMMENT(\n    /\\{/,\n    /\\}/,\n    {\n      contains: [ 'self' ]\n    }\n  );\n  return {\n    name: 'Parser3',\n    subLanguage: 'xml',\n    relevance: 0,\n    contains: [\n      hljs.COMMENT('^#', '$'),\n      hljs.COMMENT(\n        /\\^rem\\{/,\n        /\\}/,\n        {\n          relevance: 10,\n          contains: [ CURLY_SUBCOMMENT ]\n        }\n      ),\n      {\n        className: 'meta',\n        begin: '^@(?:BASE|USE|CLASS|OPTIONS)$',\n        relevance: 10\n      },\n      {\n        className: 'title',\n        begin: '@[\\\\w\\\\-]+\\\\[[\\\\w^;\\\\-]*\\\\](?:\\\\[[\\\\w^;\\\\-]*\\\\])?(?:.*)$'\n      },\n      {\n        className: 'variable',\n        begin: /\\$\\{?[\\w\\-.:]+\\}?/\n      },\n      {\n        className: 'keyword',\n        begin: /\\^[\\w\\-.:]+/\n      },\n      {\n        className: 'number',\n        begin: '\\\\^#[0-9a-fA-F]+'\n      },\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = parser3;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,QAAQ,MAAM;AACrB,YAAM,mBAAmB,KAAK;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,UACE,UAAU,CAAE,MAAO;AAAA,QACrB;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK,QAAQ,MAAM,GAAG;AAAA,UACtB,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,UAAU,CAAE,gBAAiB;AAAA,YAC/B;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}