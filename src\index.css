@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    @apply bg-dark-bg text-dark-text font-sans antialiased;
    @apply min-h-screen m-0;
    background-image: radial-gradient(
      circle at 1px 1px,
      rgba(0, 245, 255, 0.15) 1px,
      transparent 0
    );
    background-size: 20px 20px;
  }

  html {
    color-scheme: dark;
  }
}

@layer components {
  .btn-primary {
    @apply bg-neon-blue text-dark-bg px-6 py-3 rounded-lg font-medium;
    @apply hover:bg-opacity-90 transition-all duration-300;
    @apply hover:shadow-lg hover:shadow-neon-blue/25;
  }

  .btn-secondary {
    @apply border border-neon-blue text-neon-blue px-6 py-3 rounded-lg font-medium;
    @apply hover:bg-neon-blue hover:text-dark-bg transition-all duration-300;
    @apply hover:shadow-lg hover:shadow-neon-blue/25;
  }

  .card {
    @apply bg-dark-card border border-dark-border rounded-lg p-6;
    @apply hover:border-neon-blue/50 transition-all duration-300;
    @apply hover:shadow-lg hover:shadow-neon-blue/10;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-neon-blue to-neon-green bg-clip-text text-transparent;
  }

  .glow-text {
    text-shadow: 0 0 10px currentColor;
  }

  .blueprint-bg {
    @apply bg-blueprint;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
