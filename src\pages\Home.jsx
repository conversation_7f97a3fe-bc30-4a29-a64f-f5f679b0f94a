import { ArrowRight, Code2, Terminal, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';
import PostCard from '../components/blog/PostCard';
import ProjectCard from '../components/projects/ProjectCard';
import { useFeaturedPosts, useFeaturedProjects } from '../hooks/useContent';

// Featured Posts Component
function FeaturedPosts() {
  const { posts, loading, error } = useFeaturedPosts(3);

  if (loading) {
    return (
      <div className="text-center">
        <div className="text-neon-blue font-mono">
          {'>'} Carregando posts...
        </div>
      </div>
    );
  }

  if (error || posts.length === 0) {
    return (
      <div className="text-center">
        <p className="text-dark-text-secondary">
          Nenhum post em destaque no momento.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {posts.map((post) => (
        <PostCard key={post.slug} post={post} featured />
      ))}
    </div>
  );
}

// Featured Projects Component
function FeaturedProjects() {
  const { projects, loading, error } = useFeaturedProjects(3);

  if (loading) {
    return (
      <div className="text-center">
        <div className="text-neon-green font-mono">
          {'>'} Carregando projetos...
        </div>
      </div>
    );
  }

  if (error || projects.length === 0) {
    return (
      <div className="text-center">
        <p className="text-dark-text-secondary">
          Nenhum projeto em destaque no momento.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {projects.map((project) => (
        <ProjectCard key={project.id} project={project} featured />
      ))}
    </div>
  );
}

function Home() {
  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="min-h-screen flex items-center justify-center relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 bg-blueprint opacity-20"></div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Logo/Title */}
          <div className="mb-8">
            <Code2 className="h-20 w-20 text-neon-blue mx-auto mb-4 animate-glow" />
            <h1 className="text-6xl md:text-8xl font-bold text-gradient glow-text mb-4">
              BLUEPRINT
            </h1>
            <p className="text-xl md:text-2xl text-dark-text-secondary font-mono">
              O mapa. A planta. O rascunho da vida dev.
            </p>
          </div>

          {/* Manifesto */}
          <div className="mb-12">
            <p className="text-lg md:text-xl text-dark-text leading-relaxed max-w-2xl mx-auto">
              Meu laboratório digital. Meu portfólio. Meu devlog.
              <span className="text-neon-green"> Meu manifesto digital.</span>
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link to="/blog" className="btn-primary group">
              Explorar Blog
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
            <Link to="/projects" className="btn-secondary group">
              Ver Projetos
              <Zap className="ml-2 h-5 w-5 group-hover:scale-110 transition-transform" />
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Posts Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gradient mb-4">
              Últimos Artigos
            </h2>
            <p className="text-dark-text-secondary">
              Pensamentos, tutoriais e insights sobre desenvolvimento
            </p>
          </div>

          <FeaturedPosts />

          <div className="text-center mt-8">
            <Link to="/blog" className="btn-secondary">
              Ver todos os posts
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Projects Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-dark-card/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gradient mb-4">
              Projetos em Destaque
            </h2>
            <p className="text-dark-text-secondary">
              APIs, bots, SaaS e ferramentas que construí
            </p>
          </div>

          <FeaturedProjects />

          <div className="text-center mt-8">
            <Link to="/projects" className="btn-secondary">
              Ver todos os projetos
            </Link>
          </div>
        </div>
      </section>

      {/* Quick Links Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Blog Link */}
            <div className="card">
              <div className="flex items-center mb-4">
                <Terminal className="h-6 w-6 text-neon-blue mr-2" />
                <h3 className="text-xl font-semibold text-gradient">Blog</h3>
              </div>
              <p className="text-dark-text-secondary mb-4">
                Artigos técnicos, tutoriais e reflexões sobre desenvolvimento.
              </p>
              <Link
                to="/blog"
                className="text-neon-blue hover:text-neon-green transition-colors inline-flex items-center">
                Explorar blog
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>

            {/* Projects Link */}
            <div className="card">
              <div className="flex items-center mb-4">
                <Zap className="h-6 w-6 text-neon-green mr-2" />
                <h3 className="text-xl font-semibold text-gradient">
                  Projetos
                </h3>
              </div>
              <p className="text-dark-text-secondary mb-4">
                Portfólio completo com APIs, bots e ferramentas.
              </p>
              <Link
                to="/projects"
                className="text-neon-blue hover:text-neon-green transition-colors inline-flex items-center">
                Ver portfólio
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>

            {/* Devlog Link */}
            <div className="card">
              <div className="flex items-center mb-4">
                <Code2 className="h-6 w-6 text-cyber-purple mr-2" />
                <h3 className="text-xl font-semibold text-gradient">Devlog</h3>
              </div>
              <p className="text-dark-text-secondary mb-4">
                Logs públicos do desenvolvimento dos projetos.
              </p>
              <Link
                to="/devlog"
                className="text-neon-blue hover:text-neon-green transition-colors inline-flex items-center">
                Ver timeline
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* About Preview */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-dark-card/50">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gradient mb-6">
            Sobre o Blueprint
          </h2>
          <p className="text-lg text-dark-text-secondary leading-relaxed mb-8">
            Não é só um blog. É o blueprint da minha liberdade digital.
            Portfólio, laboratório, manifesto e prova viva de que estou fora da
            Matrix.
          </p>
          <Link to="/about" className="btn-secondary">
            Conhecer mais
          </Link>
        </div>
      </section>
    </div>
  );
}

export default Home;
