import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import matter from 'gray-matter';

/**
 * Calcula o tempo de leitura estimado para um texto
 * @param {string} text - Texto para calcular o tempo de leitura
 * @returns {Object} Objeto com informações de tempo de leitura
 */
function calculateReadingTime(text) {
  const wordsPerMinute = 200; // Velocidade média de leitura em português
  const words = text.trim().split(/\s+/).length;
  const minutes = Math.ceil(words / wordsPerMinute);

  return {
    text: `${minutes} min de leitura`,
    minutes: minutes,
    words: words,
  };
}

/**
 * Processa conteúdo markdown com frontmatter
 * @param {string} content - Conteúdo markdown bruto
 * @returns {Object} Conteúdo processado com metadados
 */
export function parseMarkdown(content) {
  const { data, content: markdownContent } = matter(content);

  // Calcula tempo de leitura
  const readingStats = calculateReadingTime(markdownContent);

  // Formata data se existir
  const formattedDate = data.date
    ? format(new Date(data.date), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })
    : null;

  return {
    frontmatter: {
      ...data,
      formattedDate,
      readingTime: readingStats.text,
      readingMinutes: Math.ceil(readingStats.minutes),
    },
    content: markdownContent,
    excerpt: data.excerpt || generateExcerpt(markdownContent),
  };
}

/**
 * Gera um resumo a partir do conteúdo markdown
 * @param {string} content - Conteúdo markdown
 * @param {number} maxLength - Comprimento máximo do resumo
 * @returns {string} Resumo gerado
 */
export function generateExcerpt(content, maxLength = 160) {
  // Remove sintaxe markdown e obtém texto simples
  const plainText = content
    .replace(/#{1,6}\s+/g, '') // Remove cabeçalhos
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove negrito
    .replace(/\*(.*?)\*/g, '$1') // Remove itálico
    .replace(/`(.*?)`/g, '$1') // Remove código inline
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
    .replace(/!\[.*?\]\(.*?\)/g, '') // Remove imagens
    .replace(/\n+/g, ' ') // Substitui quebras de linha por espaços
    .trim();

  if (plainText.length <= maxLength) {
    return plainText;
  }

  // Encontra a última palavra completa dentro do limite
  const truncated = plainText.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');

  return lastSpaceIndex > 0
    ? truncated.substring(0, lastSpaceIndex) + '...'
    : truncated + '...';
}

/**
 * Cria um slug a partir do título
 * @param {string} title - Título do post
 * @returns {string} Slug amigável para URL
 */
export function createSlug(title) {
  return title
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
    .replace(/\s+/g, '-') // Substitui espaços por hífens
    .replace(/-+/g, '-') // Substitui múltiplos hífens por um único
    .trim('-'); // Remove hífens do início/fim
}

/**
 * Ordena posts por data (mais recente primeiro)
 * @param {Array} posts - Array de posts
 * @returns {Array} Posts ordenados
 */
export function sortPostsByDate(posts) {
  return posts.sort((a, b) => {
    const dateA = new Date(a.frontmatter.date || 0);
    const dateB = new Date(b.frontmatter.date || 0);
    return dateB - dateA;
  });
}

/**
 * Filtra posts por categoria
 * @param {Array} posts - Array de posts
 * @param {string} category - Categoria para filtrar
 * @returns {Array} Posts filtrados
 */
export function filterPostsByCategory(posts, category) {
  if (!category) return posts;

  return posts.filter((post) => {
    const categories = post.frontmatter.categories || [];
    return categories.includes(category);
  });
}

/**
 * Filtra posts por tag
 * @param {Array} posts - Array de posts
 * @param {string} tag - Tag para filtrar
 * @returns {Array} Posts filtrados
 */
export function filterPostsByTag(posts, tag) {
  if (!tag) return posts;

  return posts.filter((post) => {
    const tags = post.frontmatter.tags || [];
    return tags.includes(tag);
  });
}

/**
 * Obtém todas as categorias únicas dos posts
 * @param {Array} posts - Array de posts
 * @returns {Array} Categorias únicas
 */
export function getAllCategories(posts) {
  const categories = new Set();

  posts.forEach((post) => {
    const postCategories = post.frontmatter.categories || [];
    postCategories.forEach((category) => categories.add(category));
  });

  return Array.from(categories).sort();
}

/**
 * Obtém todas as tags únicas dos posts
 * @param {Array} posts - Array de posts
 * @returns {Array} Tags únicas
 */
export function getAllTags(posts) {
  const tags = new Set();

  posts.forEach((post) => {
    const postTags = post.frontmatter.tags || [];
    postTags.forEach((tag) => tags.add(tag));
  });

  return Array.from(tags).sort();
}
