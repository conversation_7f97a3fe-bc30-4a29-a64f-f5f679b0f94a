{"version": 3, "sources": ["../../refractor/lang/css.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = css\ncss.displayName = 'css'\ncss.aliases = []\nfunction css(Prism) {\n  ;(function (Prism) {\n    var string =\n      /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/\n    Prism.languages.css = {\n      comment: /\\/\\*[\\s\\S]*?\\*\\//,\n      atrule: {\n        pattern: /@[\\w-](?:[^;{\\s]|\\s+(?![\\s{]))*(?:;|(?=\\s*\\{))/,\n        inside: {\n          rule: /^@[\\w-]+/,\n          'selector-function-argument': {\n            pattern:\n              /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n            lookbehind: true,\n            alias: 'selector'\n          },\n          keyword: {\n            pattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n            lookbehind: true\n          } // See rest below\n        }\n      },\n      url: {\n        // https://drafts.csswg.org/css-values-3/#urls\n        pattern: RegExp(\n          '\\\\burl\\\\((?:' +\n            string.source +\n            '|' +\n            /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source +\n            ')\\\\)',\n          'i'\n        ),\n        greedy: true,\n        inside: {\n          function: /^url/i,\n          punctuation: /^\\(|\\)$/,\n          string: {\n            pattern: RegExp('^' + string.source + '$'),\n            alias: 'url'\n          }\n        }\n      },\n      selector: {\n        pattern: RegExp(\n          '(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' +\n            string.source +\n            ')*(?=\\\\s*\\\\{)'\n        ),\n        lookbehind: true\n      },\n      string: {\n        pattern: string,\n        greedy: true\n      },\n      property: {\n        pattern:\n          /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n        lookbehind: true\n      },\n      important: /!important\\b/i,\n      function: {\n        pattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n        lookbehind: true\n      },\n      punctuation: /[(){};:,]/\n    }\n    Prism.languages.css['atrule'].inside.rest = Prism.languages.css\n    var markup = Prism.languages.markup\n    if (markup) {\n      markup.tag.addInlined('style', 'css')\n      markup.tag.addAttribute('style', 'css')\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,SACF;AACF,QAAAA,OAAM,UAAU,MAAM;AAAA,UACpB,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,MAAM;AAAA,cACN,8BAA8B;AAAA,gBAC5B,SACE;AAAA,gBACF,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,cACA,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA;AAAA,YAEH,SAAS;AAAA,cACP,iBACE,OAAO,SACP,MACA,8BAA8B,SAC9B;AAAA,cACF;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,UAAU;AAAA,cACV,aAAa;AAAA,cACb,QAAQ;AAAA,gBACN,SAAS,OAAO,MAAM,OAAO,SAAS,GAAG;AAAA,gBACzC,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,cACP,sDACE,OAAO,SACP;AAAA,YACJ;AAAA,YACA,YAAY;AAAA,UACd;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,UAAU;AAAA,YACR,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,WAAW;AAAA,UACX,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,aAAa;AAAA,QACf;AACA,QAAAA,OAAM,UAAU,IAAI,QAAQ,EAAE,OAAO,OAAOA,OAAM,UAAU;AAC5D,YAAI,SAASA,OAAM,UAAU;AAC7B,YAAI,QAAQ;AACV,iBAAO,IAAI,WAAW,SAAS,KAAK;AACpC,iBAAO,IAAI,aAAa,SAAS,KAAK;AAAA,QACxC;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}