{"version": 3, "sources": ["../../refractor/lang/qore.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = qore\nqore.displayName = 'qore'\nqore.aliases = []\nfunction qore(Prism) {\n  Prism.languages.qore = Prism.languages.extend('clike', {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|(?:\\/\\/|#).*)/,\n      lookbehind: true\n    },\n    // Overridden to allow unescaped multi-line strings\n    string: {\n      pattern: /(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:abstract|any|assert|binary|bool|boolean|break|byte|case|catch|char|class|code|const|continue|data|default|do|double|else|enum|extends|final|finally|float|for|goto|hash|if|implements|import|inherits|instanceof|int|interface|long|my|native|new|nothing|null|object|our|own|private|reference|rethrow|return|short|soft(?:bool|date|float|int|list|number|string)|static|strictfp|string|sub|super|switch|synchronized|this|throw|throws|transient|try|void|volatile|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/i,\n    function: /\\$?\\b(?!\\d)\\w+(?=\\()/,\n    number:\n      /\\b(?:0b[01]+|0x(?:[\\da-f]*\\.)?[\\da-fp\\-]+|(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:e\\d+)?[df]|(?:\\d+(?:\\.\\d+)?|\\.\\d+))\\b/i,\n    operator: {\n      pattern:\n        /(^|[^.])(?:\\+[+=]?|-[-=]?|[!=](?:==?|~)?|>>?=?|<(?:=>?|<=?)?|&[&=]?|\\|[|=]?|[*\\/%^]=?|[~?])/,\n      lookbehind: true\n    },\n    variable: /\\$(?!\\d)\\w+\\b/\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,UAAU,OAAO,MAAM,UAAU,OAAO,SAAS;AAAA,QACrD,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA;AAAA,QAEA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QACE;AAAA,QACF,UAAU;AAAA,UACR,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}