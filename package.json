{"name": "blueprint-blog", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "gray-matter": "^4.0.3", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.1", "reading-time": "^1.5.0", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}