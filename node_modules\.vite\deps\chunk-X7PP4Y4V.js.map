{"version": 3, "sources": ["../../refractor/lang/peoplecode.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = peoplecode\npeoplecode.displayName = 'peoplecode'\npeoplecode.aliases = ['pcode']\nfunction peoplecode(Prism) {\n  Prism.languages.peoplecode = {\n    comment: RegExp(\n      [\n        // C-style multiline comments\n        /\\/\\*[\\s\\S]*?\\*\\//.source, // REM comments\n        /\\bREM[^;]*;/.source, // Nested <* *> comments\n        /<\\*(?:[^<*]|\\*(?!>)|<(?!\\*)|<\\*(?:(?!\\*>)[\\s\\S])*\\*>)*\\*>/.source, // /+ +/ comments\n        /\\/\\+[\\s\\S]*?\\+\\//.source\n      ].join('|')\n    ),\n    string: {\n      pattern: /'(?:''|[^'\\r\\n])*'(?!')|\"(?:\"\"|[^\"\\r\\n])*\"(?!\")/,\n      greedy: true\n    },\n    variable: /%\\w+/,\n    'function-definition': {\n      pattern: /((?:^|[^\\w-])(?:function|method)\\s+)\\w+/i,\n      lookbehind: true,\n      alias: 'function'\n    },\n    'class-name': {\n      pattern:\n        /((?:^|[^-\\w])(?:as|catch|class|component|create|extends|global|implements|instance|local|of|property|returns)\\s+)\\w+(?::\\w+)*/i,\n      lookbehind: true,\n      inside: {\n        punctuation: /:/\n      }\n    },\n    keyword:\n      /\\b(?:abstract|alias|as|catch|class|component|constant|create|declare|else|end-(?:class|evaluate|for|function|get|if|method|set|try|while)|evaluate|extends|for|function|get|global|if|implements|import|instance|library|local|method|null|of|out|peopleCode|private|program|property|protected|readonly|ref|repeat|returns?|set|step|then|throw|to|try|until|value|when(?:-other)?|while)\\b/i,\n    'operator-keyword': {\n      pattern: /\\b(?:and|not|or)\\b/i,\n      alias: 'operator'\n    },\n    function: /[_a-z]\\w*(?=\\s*\\()/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    number: /\\b\\d+(?:\\.\\d+)?\\b/,\n    operator: /<>|[<>]=?|!=|\\*\\*|[-+*/|=@]/,\n    punctuation: /[:.;,()[\\]]/\n  }\n  Prism.languages.pcode = Prism.languages.peoplecode\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,eAAW,cAAc;AACzB,eAAW,UAAU,CAAC,OAAO;AAC7B,aAAS,WAAW,OAAO;AACzB,YAAM,UAAU,aAAa;AAAA,QAC3B,SAAS;AAAA,UACP;AAAA;AAAA,YAEE,mBAAmB;AAAA;AAAA,YACnB,cAAc;AAAA;AAAA,YACd,4DAA4D;AAAA;AAAA,YAC5D,mBAAmB;AAAA,UACrB,EAAE,KAAK,GAAG;AAAA,QACZ;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,UAAU;AAAA,QACV,uBAAuB;AAAA,UACrB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,cAAc;AAAA,UACZ,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,SACE;AAAA,QACF,oBAAoB;AAAA,UAClB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AACA,YAAM,UAAU,QAAQ,MAAM,UAAU;AAAA,IAC1C;AAAA;AAAA;", "names": []}