{"version": 3, "sources": ["../../highlight.js/lib/languages/tap.js"], "sourcesContent": ["/*\nLanguage: Test Anything Protocol\nDescription: TAP, the Test Anything Protocol, is a simple text-based interface between testing modules in a test harness.\nRequires: yaml.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://testanything.org\n*/\n\nfunction tap(hljs) {\n  return {\n    name: 'Test Anything Protocol',\n    case_insensitive: true,\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      // version of format and total amount of testcases\n      {\n        className: 'meta',\n        variants: [\n          {\n            begin: '^TAP version (\\\\d+)$'\n          },\n          {\n            begin: '^1\\\\.\\\\.(\\\\d+)$'\n          }\n        ]\n      },\n      // YAML block\n      {\n        begin: /---$/,\n        end: '\\\\.\\\\.\\\\.$',\n        subLanguage: 'yaml',\n        relevance: 0\n      },\n      // testcase number\n      {\n        className: 'number',\n        begin: ' (\\\\d+) '\n      },\n      // testcase status and description\n      {\n        className: 'symbol',\n        variants: [\n          {\n            begin: '^ok'\n          },\n          {\n            begin: '^not ok'\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = tap;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,KAAK;AAAA;AAAA,UAEL;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,aAAa;AAAA,YACb,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}