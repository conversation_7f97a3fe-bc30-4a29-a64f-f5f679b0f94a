{"version": 3, "sources": ["../../highlight.js/lib/languages/swift.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\nconst keywordWrapper = keyword => concat(\n  /\\b/,\n  keyword,\n  /\\w$/.test(keyword) ? /\\b/ : /\\B/\n);\n\n// Keywords that require a leading dot.\nconst dotKeywords = [\n  'Protocol', // contextual\n  'Type' // contextual\n].map(keywordWrapper);\n\n// Keywords that may have a leading dot.\nconst optionalDotKeywords = [\n  'init',\n  'self'\n].map(keywordWrapper);\n\n// should register as keyword, not type\nconst keywordTypes = [\n  'Any',\n  'Self'\n];\n\n// Regular keywords and literals.\nconst keywords = [\n  // strings below will be fed into the regular `keywords` engine while regex\n  // will result in additional modes being created to scan for those keywords to\n  // avoid conflicts with other rules\n  'associatedtype',\n  'async',\n  'await',\n  /as\\?/, // operator\n  /as!/, // operator\n  'as', // operator\n  'break',\n  'case',\n  'catch',\n  'class',\n  'continue',\n  'convenience', // contextual\n  'default',\n  'defer',\n  'deinit',\n  'didSet', // contextual\n  'do',\n  'dynamic', // contextual\n  'else',\n  'enum',\n  'extension',\n  'fallthrough',\n  /fileprivate\\(set\\)/,\n  'fileprivate',\n  'final', // contextual\n  'for',\n  'func',\n  'get', // contextual\n  'guard',\n  'if',\n  'import',\n  'indirect', // contextual\n  'infix', // contextual\n  /init\\?/,\n  /init!/,\n  'inout',\n  /internal\\(set\\)/,\n  'internal',\n  'in',\n  'is', // operator\n  'lazy', // contextual\n  'let',\n  'mutating', // contextual\n  'nonmutating', // contextual\n  /open\\(set\\)/, // contextual\n  'open', // contextual\n  'operator',\n  'optional', // contextual\n  'override', // contextual\n  'postfix', // contextual\n  'precedencegroup',\n  'prefix', // contextual\n  /private\\(set\\)/,\n  'private',\n  'protocol',\n  /public\\(set\\)/,\n  'public',\n  'repeat',\n  'required', // contextual\n  'rethrows',\n  'return',\n  'set', // contextual\n  'some', // contextual\n  'static',\n  'struct',\n  'subscript',\n  'super',\n  'switch',\n  'throws',\n  'throw',\n  /try\\?/, // operator\n  /try!/, // operator\n  'try', // operator\n  'typealias',\n  /unowned\\(safe\\)/, // contextual\n  /unowned\\(unsafe\\)/, // contextual\n  'unowned', // contextual\n  'var',\n  'weak', // contextual\n  'where',\n  'while',\n  'willSet' // contextual\n];\n\n// NOTE: Contextual keywords are reserved only in specific contexts.\n// Ideally, these should be matched using modes to avoid false positives.\n\n// Literals.\nconst literals = [\n  'false',\n  'nil',\n  'true'\n];\n\n// Keywords used in precedence groups.\nconst precedencegroupKeywords = [\n  'assignment',\n  'associativity',\n  'higherThan',\n  'left',\n  'lowerThan',\n  'none',\n  'right'\n];\n\n// Keywords that start with a number sign (#).\n// #available is handled separately.\nconst numberSignKeywords = [\n  '#colorLiteral',\n  '#column',\n  '#dsohandle',\n  '#else',\n  '#elseif',\n  '#endif',\n  '#error',\n  '#file',\n  '#fileID',\n  '#fileLiteral',\n  '#filePath',\n  '#function',\n  '#if',\n  '#imageLiteral',\n  '#keyPath',\n  '#line',\n  '#selector',\n  '#sourceLocation',\n  '#warn_unqualified_access',\n  '#warning'\n];\n\n// Global functions in the Standard Library.\nconst builtIns = [\n  'abs',\n  'all',\n  'any',\n  'assert',\n  'assertionFailure',\n  'debugPrint',\n  'dump',\n  'fatalError',\n  'getVaList',\n  'isKnownUniquelyReferenced',\n  'max',\n  'min',\n  'numericCast',\n  'pointwiseMax',\n  'pointwiseMin',\n  'precondition',\n  'preconditionFailure',\n  'print',\n  'readLine',\n  'repeatElement',\n  'sequence',\n  'stride',\n  'swap',\n  'swift_unboxFromSwiftValueWithType',\n  'transcode',\n  'type',\n  'unsafeBitCast',\n  'unsafeDowncast',\n  'withExtendedLifetime',\n  'withUnsafeMutablePointer',\n  'withUnsafePointer',\n  'withVaList',\n  'withoutActuallyEscaping',\n  'zip'\n];\n\n// Valid first characters for operators.\nconst operatorHead = either(\n  /[/=\\-+!*%<>&|^~?]/,\n  /[\\u00A1-\\u00A7]/,\n  /[\\u00A9\\u00AB]/,\n  /[\\u00AC\\u00AE]/,\n  /[\\u00B0\\u00B1]/,\n  /[\\u00B6\\u00BB\\u00BF\\u00D7\\u00F7]/,\n  /[\\u2016-\\u2017]/,\n  /[\\u2020-\\u2027]/,\n  /[\\u2030-\\u203E]/,\n  /[\\u2041-\\u2053]/,\n  /[\\u2055-\\u205E]/,\n  /[\\u2190-\\u23FF]/,\n  /[\\u2500-\\u2775]/,\n  /[\\u2794-\\u2BFF]/,\n  /[\\u2E00-\\u2E7F]/,\n  /[\\u3001-\\u3003]/,\n  /[\\u3008-\\u3020]/,\n  /[\\u3030]/\n);\n\n// Valid characters for operators.\nconst operatorCharacter = either(\n  operatorHead,\n  /[\\u0300-\\u036F]/,\n  /[\\u1DC0-\\u1DFF]/,\n  /[\\u20D0-\\u20FF]/,\n  /[\\uFE00-\\uFE0F]/,\n  /[\\uFE20-\\uFE2F]/\n  // TODO: The following characters are also allowed, but the regex isn't supported yet.\n  // /[\\u{E0100}-\\u{E01EF}]/u\n);\n\n// Valid operator.\nconst operator = concat(operatorHead, operatorCharacter, '*');\n\n// Valid first characters for identifiers.\nconst identifierHead = either(\n  /[a-zA-Z_]/,\n  /[\\u00A8\\u00AA\\u00AD\\u00AF\\u00B2-\\u00B5\\u00B7-\\u00BA]/,\n  /[\\u00BC-\\u00BE\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u00FF]/,\n  /[\\u0100-\\u02FF\\u0370-\\u167F\\u1681-\\u180D\\u180F-\\u1DBF]/,\n  /[\\u1E00-\\u1FFF]/,\n  /[\\u200B-\\u200D\\u202A-\\u202E\\u203F-\\u2040\\u2054\\u2060-\\u206F]/,\n  /[\\u2070-\\u20CF\\u2100-\\u218F\\u2460-\\u24FF\\u2776-\\u2793]/,\n  /[\\u2C00-\\u2DFF\\u2E80-\\u2FFF]/,\n  /[\\u3004-\\u3007\\u3021-\\u302F\\u3031-\\u303F\\u3040-\\uD7FF]/,\n  /[\\uF900-\\uFD3D\\uFD40-\\uFDCF\\uFDF0-\\uFE1F\\uFE30-\\uFE44]/,\n  /[\\uFE47-\\uFEFE\\uFF00-\\uFFFD]/ // Should be /[\\uFE47-\\uFFFD]/, but we have to exclude FEFF.\n  // The following characters are also allowed, but the regexes aren't supported yet.\n  // /[\\u{10000}-\\u{1FFFD}\\u{20000-\\u{2FFFD}\\u{30000}-\\u{3FFFD}\\u{40000}-\\u{4FFFD}]/u,\n  // /[\\u{50000}-\\u{5FFFD}\\u{60000-\\u{6FFFD}\\u{70000}-\\u{7FFFD}\\u{80000}-\\u{8FFFD}]/u,\n  // /[\\u{90000}-\\u{9FFFD}\\u{A0000-\\u{AFFFD}\\u{B0000}-\\u{BFFFD}\\u{C0000}-\\u{CFFFD}]/u,\n  // /[\\u{D0000}-\\u{DFFFD}\\u{E0000-\\u{EFFFD}]/u\n);\n\n// Valid characters for identifiers.\nconst identifierCharacter = either(\n  identifierHead,\n  /\\d/,\n  /[\\u0300-\\u036F\\u1DC0-\\u1DFF\\u20D0-\\u20FF\\uFE20-\\uFE2F]/\n);\n\n// Valid identifier.\nconst identifier = concat(identifierHead, identifierCharacter, '*');\n\n// Valid type identifier.\nconst typeIdentifier = concat(/[A-Z]/, identifierCharacter, '*');\n\n// Built-in attributes, which are highlighted as keywords.\n// @available is handled separately.\nconst keywordAttributes = [\n  'autoclosure',\n  concat(/convention\\(/, either('swift', 'block', 'c'), /\\)/),\n  'discardableResult',\n  'dynamicCallable',\n  'dynamicMemberLookup',\n  'escaping',\n  'frozen',\n  'GKInspectable',\n  'IBAction',\n  'IBDesignable',\n  'IBInspectable',\n  'IBOutlet',\n  'IBSegueAction',\n  'inlinable',\n  'main',\n  'nonobjc',\n  'NSApplicationMain',\n  'NSCopying',\n  'NSManaged',\n  concat(/objc\\(/, identifier, /\\)/),\n  'objc',\n  'objcMembers',\n  'propertyWrapper',\n  'requires_stored_property_inits',\n  'testable',\n  'UIApplicationMain',\n  'unknown',\n  'usableFromInline'\n];\n\n// Contextual keywords used in @available and #available.\nconst availabilityKeywords = [\n  'iOS',\n  'iOSApplicationExtension',\n  'macOS',\n  'macOSApplicationExtension',\n  'macCatalyst',\n  'macCatalystApplicationExtension',\n  'watchOS',\n  'watchOSApplicationExtension',\n  'tvOS',\n  'tvOSApplicationExtension',\n  'swift'\n];\n\n/*\nLanguage: Swift\nDescription: Swift is a general-purpose programming language built using a modern approach to safety, performance, and software design patterns.\nAuthor: Steven Van Impe <<EMAIL>>\nContributors: Chris Eidhof <<EMAIL>>, Nate Cook <<EMAIL>>, Alexander Lichter <<EMAIL>>, Richard Gibson <gibson042@github>\nWebsite: https://swift.org\nCategory: common, system\n*/\n\n/** @type LanguageFn */\nfunction swift(hljs) {\n  const WHITESPACE = {\n    match: /\\s+/,\n    relevance: 0\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID411\n  const BLOCK_COMMENT = hljs.COMMENT(\n    '/\\\\*',\n    '\\\\*/',\n    {\n      contains: [ 'self' ]\n    }\n  );\n  const COMMENTS = [\n    hljs.C_LINE_COMMENT_MODE,\n    BLOCK_COMMENT\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID413\n  // https://docs.swift.org/swift-book/ReferenceManual/zzSummaryOfTheGrammar.html\n  const DOT_KEYWORD = {\n    className: 'keyword',\n    begin: concat(/\\./, lookahead(either(...dotKeywords, ...optionalDotKeywords))),\n    end: either(...dotKeywords, ...optionalDotKeywords),\n    excludeBegin: true\n  };\n  const KEYWORD_GUARD = {\n    // Consume .keyword to prevent highlighting properties and methods as keywords.\n    match: concat(/\\./, either(...keywords)),\n    relevance: 0\n  };\n  const PLAIN_KEYWORDS = keywords\n    .filter(kw => typeof kw === 'string')\n    .concat([ \"_|0\" ]); // seems common, so 0 relevance\n  const REGEX_KEYWORDS = keywords\n    .filter(kw => typeof kw !== 'string') // find regex\n    .concat(keywordTypes)\n    .map(keywordWrapper);\n  const KEYWORD = {\n    variants: [\n      {\n        className: 'keyword',\n        match: either(...REGEX_KEYWORDS, ...optionalDotKeywords)\n      }\n    ]\n  };\n  // find all the regular keywords\n  const KEYWORDS = {\n    $pattern: either(\n      /\\b\\w+/, // regular keywords\n      /#\\w+/ // number keywords\n    ),\n    keyword: PLAIN_KEYWORDS\n      .concat(numberSignKeywords),\n    literal: literals\n  };\n  const KEYWORD_MODES = [\n    DOT_KEYWORD,\n    KEYWORD_GUARD,\n    KEYWORD\n  ];\n\n  // https://github.com/apple/swift/tree/main/stdlib/public/core\n  const BUILT_IN_GUARD = {\n    // Consume .built_in to prevent highlighting properties and methods.\n    match: concat(/\\./, either(...builtIns)),\n    relevance: 0\n  };\n  const BUILT_IN = {\n    className: 'built_in',\n    match: concat(/\\b/, either(...builtIns), /(?=\\()/)\n  };\n  const BUILT_INS = [\n    BUILT_IN_GUARD,\n    BUILT_IN\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID418\n  const OPERATOR_GUARD = {\n    // Prevent -> from being highlighting as an operator.\n    match: /->/,\n    relevance: 0\n  };\n  const OPERATOR = {\n    className: 'operator',\n    relevance: 0,\n    variants: [\n      {\n        match: operator\n      },\n      {\n        // dot-operator: only operators that start with a dot are allowed to use dots as\n        // characters (..., ...<, .*, etc). So there rule here is: a dot followed by one or more\n        // characters that may also include dots.\n        match: `\\\\.(\\\\.|${operatorCharacter})+`\n      }\n    ]\n  };\n  const OPERATORS = [\n    OPERATOR_GUARD,\n    OPERATOR\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#grammar_numeric-literal\n  // TODO: Update for leading `-` after lookbehind is supported everywhere\n  const decimalDigits = '([0-9]_*)+';\n  const hexDigits = '([0-9a-fA-F]_*)+';\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // decimal floating-point-literal (subsumes decimal-literal)\n      {\n        match: `\\\\b(${decimalDigits})(\\\\.(${decimalDigits}))?` + `([eE][+-]?(${decimalDigits}))?\\\\b`\n      },\n      // hexadecimal floating-point-literal (subsumes hexadecimal-literal)\n      {\n        match: `\\\\b0x(${hexDigits})(\\\\.(${hexDigits}))?` + `([pP][+-]?(${decimalDigits}))?\\\\b`\n      },\n      // octal-literal\n      {\n        match: /\\b0o([0-7]_*)+\\b/\n      },\n      // binary-literal\n      {\n        match: /\\b0b([01]_*)+\\b/\n      }\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#grammar_string-literal\n  const ESCAPED_CHARACTER = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    variants: [\n      {\n        match: concat(/\\\\/, rawDelimiter, /[0\\\\tnr\"']/)\n      },\n      {\n        match: concat(/\\\\/, rawDelimiter, /u\\{[0-9a-fA-F]{1,8}\\}/)\n      }\n    ]\n  });\n  const ESCAPED_NEWLINE = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    match: concat(/\\\\/, rawDelimiter, /[\\t ]*(?:[\\r\\n]|\\r\\n)/)\n  });\n  const INTERPOLATION = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    label: \"interpol\",\n    begin: concat(/\\\\/, rawDelimiter, /\\(/),\n    end: /\\)/\n  });\n  const MULTILINE_STRING = (rawDelimiter = \"\") => ({\n    begin: concat(rawDelimiter, /\"\"\"/),\n    end: concat(/\"\"\"/, rawDelimiter),\n    contains: [\n      ESCAPED_CHARACTER(rawDelimiter),\n      ESCAPED_NEWLINE(rawDelimiter),\n      INTERPOLATION(rawDelimiter)\n    ]\n  });\n  const SINGLE_LINE_STRING = (rawDelimiter = \"\") => ({\n    begin: concat(rawDelimiter, /\"/),\n    end: concat(/\"/, rawDelimiter),\n    contains: [\n      ESCAPED_CHARACTER(rawDelimiter),\n      INTERPOLATION(rawDelimiter)\n    ]\n  });\n  const STRING = {\n    className: 'string',\n    variants: [\n      MULTILINE_STRING(),\n      MULTILINE_STRING(\"#\"),\n      MULTILINE_STRING(\"##\"),\n      MULTILINE_STRING(\"###\"),\n      SINGLE_LINE_STRING(),\n      SINGLE_LINE_STRING(\"#\"),\n      SINGLE_LINE_STRING(\"##\"),\n      SINGLE_LINE_STRING(\"###\")\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID412\n  const QUOTED_IDENTIFIER = {\n    match: concat(/`/, identifier, /`/)\n  };\n  const IMPLICIT_PARAMETER = {\n    className: 'variable',\n    match: /\\$\\d+/\n  };\n  const PROPERTY_WRAPPER_PROJECTION = {\n    className: 'variable',\n    match: `\\\\$${identifierCharacter}+`\n  };\n  const IDENTIFIERS = [\n    QUOTED_IDENTIFIER,\n    IMPLICIT_PARAMETER,\n    PROPERTY_WRAPPER_PROJECTION\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Attributes.html\n  const AVAILABLE_ATTRIBUTE = {\n    match: /(@|#)available/,\n    className: \"keyword\",\n    starts: {\n      contains: [\n        {\n          begin: /\\(/,\n          end: /\\)/,\n          keywords: availabilityKeywords,\n          contains: [\n            ...OPERATORS,\n            NUMBER,\n            STRING\n          ]\n        }\n      ]\n    }\n  };\n  const KEYWORD_ATTRIBUTE = {\n    className: 'keyword',\n    match: concat(/@/, either(...keywordAttributes))\n  };\n  const USER_DEFINED_ATTRIBUTE = {\n    className: 'meta',\n    match: concat(/@/, identifier)\n  };\n  const ATTRIBUTES = [\n    AVAILABLE_ATTRIBUTE,\n    KEYWORD_ATTRIBUTE,\n    USER_DEFINED_ATTRIBUTE\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Types.html\n  const TYPE = {\n    match: lookahead(/\\b[A-Z]/),\n    relevance: 0,\n    contains: [\n      { // Common Apple frameworks, for relevance boost\n        className: 'type',\n        match: concat(/(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)/, identifierCharacter, '+')\n      },\n      { // Type identifier\n        className: 'type',\n        match: typeIdentifier,\n        relevance: 0\n      },\n      { // Optional type\n        match: /[?!]+/,\n        relevance: 0\n      },\n      { // Variadic parameter\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      { // Protocol composition\n        match: concat(/\\s+&\\s+/, lookahead(typeIdentifier)),\n        relevance: 0\n      }\n    ]\n  };\n  const GENERIC_ARGUMENTS = {\n    begin: /</,\n    end: />/,\n    keywords: KEYWORDS,\n    contains: [\n      ...COMMENTS,\n      ...KEYWORD_MODES,\n      ...ATTRIBUTES,\n      OPERATOR_GUARD,\n      TYPE\n    ]\n  };\n  TYPE.contains.push(GENERIC_ARGUMENTS);\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Expressions.html#ID552\n  // Prevents element names from being highlighted as keywords.\n  const TUPLE_ELEMENT_NAME = {\n    match: concat(identifier, /\\s*:/),\n    keywords: \"_|0\",\n    relevance: 0\n  };\n  // Matches tuples as well as the parameter list of a function type.\n  const TUPLE = {\n    begin: /\\(/,\n    end: /\\)/,\n    relevance: 0,\n    keywords: KEYWORDS,\n    contains: [\n      'self',\n      TUPLE_ELEMENT_NAME,\n      ...COMMENTS,\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS,\n      ...ATTRIBUTES,\n      TYPE\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID362\n  // Matches both the keyword func and the function title.\n  // Grouping these lets us differentiate between the operator function <\n  // and the start of the generic parameter clause (also <).\n  const FUNC_PLUS_TITLE = {\n    beginKeywords: 'func',\n    contains: [\n      {\n        className: 'title',\n        match: either(QUOTED_IDENTIFIER.match, identifier, operator),\n        // Required to make sure the opening < of the generic parameter clause\n        // isn't parsed as a second title.\n        endsParent: true,\n        relevance: 0\n      },\n      WHITESPACE\n    ]\n  };\n  const GENERIC_PARAMETERS = {\n    begin: /</,\n    end: />/,\n    contains: [\n      ...COMMENTS,\n      TYPE\n    ]\n  };\n  const FUNCTION_PARAMETER_NAME = {\n    begin: either(\n      lookahead(concat(identifier, /\\s*:/)),\n      lookahead(concat(identifier, /\\s+/, identifier, /\\s*:/))\n    ),\n    end: /:/,\n    relevance: 0,\n    contains: [\n      {\n        className: 'keyword',\n        match: /\\b_\\b/\n      },\n      {\n        className: 'params',\n        match: identifier\n      }\n    ]\n  };\n  const FUNCTION_PARAMETERS = {\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS,\n    contains: [\n      FUNCTION_PARAMETER_NAME,\n      ...COMMENTS,\n      ...KEYWORD_MODES,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...ATTRIBUTES,\n      TYPE,\n      TUPLE\n    ],\n    endsParent: true,\n    illegal: /[\"']/\n  };\n  const FUNCTION = {\n    className: 'function',\n    match: lookahead(/\\bfunc\\b/),\n    contains: [\n      FUNC_PLUS_TITLE,\n      GENERIC_PARAMETERS,\n      FUNCTION_PARAMETERS,\n      WHITESPACE\n    ],\n    illegal: [\n      /\\[/,\n      /%/\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID375\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID379\n  const INIT_SUBSCRIPT = {\n    className: 'function',\n    match: /\\b(subscript|init[?!]?)\\s*(?=[<(])/,\n    keywords: {\n      keyword: \"subscript init init? init!\",\n      $pattern: /\\w+[?!]?/\n    },\n    contains: [\n      GENERIC_PARAMETERS,\n      FUNCTION_PARAMETERS,\n      WHITESPACE\n    ],\n    illegal: /\\[|%/\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID380\n  const OPERATOR_DECLARATION = {\n    beginKeywords: 'operator',\n    end: hljs.MATCH_NOTHING_RE,\n    contains: [\n      {\n        className: 'title',\n        match: operator,\n        endsParent: true,\n        relevance: 0\n      }\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID550\n  const PRECEDENCEGROUP = {\n    beginKeywords: 'precedencegroup',\n    end: hljs.MATCH_NOTHING_RE,\n    contains: [\n      {\n        className: 'title',\n        match: typeIdentifier,\n        relevance: 0\n      },\n      {\n        begin: /{/,\n        end: /}/,\n        relevance: 0,\n        endsParent: true,\n        keywords: [\n          ...precedencegroupKeywords,\n          ...literals\n        ],\n        contains: [ TYPE ]\n      }\n    ]\n  };\n\n  // Add supported submodes to string interpolation.\n  for (const variant of STRING.variants) {\n    const interpolation = variant.contains.find(mode => mode.label === \"interpol\");\n    // TODO: Interpolation can contain any expression, so there's room for improvement here.\n    interpolation.keywords = KEYWORDS;\n    const submodes = [\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS\n    ];\n    interpolation.contains = [\n      ...submodes,\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\n          'self',\n          ...submodes\n        ]\n      }\n    ];\n  }\n\n  return {\n    name: 'Swift',\n    keywords: KEYWORDS,\n    contains: [\n      ...COMMENTS,\n      FUNCTION,\n      INIT_SUBSCRIPT,\n      {\n        className: 'class',\n        beginKeywords: 'struct protocol class extension enum',\n        end: '\\\\{',\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: /[A-Za-z$_][\\u00C0-\\u02B80-9A-Za-z$_]*/\n          }),\n          ...KEYWORD_MODES\n        ]\n      },\n      OPERATOR_DECLARATION,\n      PRECEDENCEGROUP,\n      {\n        beginKeywords: 'import',\n        end: /$/,\n        contains: [ ...COMMENTS ],\n        relevance: 0\n      },\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS,\n      ...ATTRIBUTES,\n      TYPE,\n      TUPLE\n    ]\n  };\n}\n\nmodule.exports = swift;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,IAAI;AACrB,aAAO,OAAO,OAAO,IAAI,GAAG;AAAA,IAC9B;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AASA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC5D,aAAO;AAAA,IACT;AAEA,QAAM,iBAAiB,aAAW;AAAA,MAChC;AAAA,MACA;AAAA,MACA,MAAM,KAAK,OAAO,IAAI,OAAO;AAAA,IAC/B;AAGA,QAAM,cAAc;AAAA,MAClB;AAAA;AAAA,MACA;AAAA;AAAA,IACF,EAAE,IAAI,cAAc;AAGpB,QAAM,sBAAsB;AAAA,MAC1B;AAAA,MACA;AAAA,IACF,EAAE,IAAI,cAAc;AAGpB,QAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,IACF;AAGA,QAAM,WAAW;AAAA;AAAA;AAAA;AAAA,MAIf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IACF;AAMA,QAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,0BAA0B;AAAA,MAC9B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAIA,QAAM,qBAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,oBAAoB;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,IAGF;AAGA,QAAM,WAAW,OAAO,cAAc,mBAAmB,GAAG;AAG5D,QAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMF;AAGA,QAAM,sBAAsB;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,aAAa,OAAO,gBAAgB,qBAAqB,GAAG;AAGlE,QAAM,iBAAiB,OAAO,SAAS,qBAAqB,GAAG;AAI/D,QAAM,oBAAoB;AAAA,MACxB;AAAA,MACA,OAAO,gBAAgB,OAAO,SAAS,SAAS,GAAG,GAAG,IAAI;AAAA,MAC1D;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,UAAU,YAAY,IAAI;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,uBAAuB;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAYA,aAAS,MAAM,MAAM;AACnB,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,gBAAgB,KAAK;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,UACE,UAAU,CAAE,MAAO;AAAA,QACrB;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf,KAAK;AAAA,QACL;AAAA,MACF;AAIA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO,OAAO,MAAM,UAAU,OAAO,GAAG,aAAa,GAAG,mBAAmB,CAAC,CAAC;AAAA,QAC7E,KAAK,OAAO,GAAG,aAAa,GAAG,mBAAmB;AAAA,QAClD,cAAc;AAAA,MAChB;AACA,YAAM,gBAAgB;AAAA;AAAA,QAEpB,OAAO,OAAO,MAAM,OAAO,GAAG,QAAQ,CAAC;AAAA,QACvC,WAAW;AAAA,MACb;AACA,YAAM,iBAAiB,SACpB,OAAO,QAAM,OAAO,OAAO,QAAQ,EACnC,OAAO,CAAE,KAAM,CAAC;AACnB,YAAM,iBAAiB,SACpB,OAAO,QAAM,OAAO,OAAO,QAAQ,EACnC,OAAO,YAAY,EACnB,IAAI,cAAc;AACrB,YAAM,UAAU;AAAA,QACd,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO,OAAO,GAAG,gBAAgB,GAAG,mBAAmB;AAAA,UACzD;AAAA,QACF;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,UACR;AAAA;AAAA,UACA;AAAA;AAAA,QACF;AAAA,QACA,SAAS,eACN,OAAO,kBAAkB;AAAA,QAC5B,SAAS;AAAA,MACX;AACA,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,iBAAiB;AAAA;AAAA,QAErB,OAAO,OAAO,MAAM,OAAO,GAAG,QAAQ,CAAC;AAAA,QACvC,WAAW;AAAA,MACb;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO,OAAO,MAAM,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAAA,MACnD;AACA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,MACF;AAGA,YAAM,iBAAiB;AAAA;AAAA,QAErB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,YAIE,OAAO,WAAW,iBAAiB;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,MACF;AAIA,YAAM,gBAAgB;AACtB,YAAM,YAAY;AAClB,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA;AAAA,UAER;AAAA,YACE,OAAO,OAAO,aAAa,SAAS,aAAa,iBAAsB,aAAa;AAAA,UACtF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO,SAAS,SAAS,SAAS,SAAS,iBAAsB,aAAa;AAAA,UAChF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,UACT;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAGA,YAAM,oBAAoB,CAAC,eAAe,QAAQ;AAAA,QAChD,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO,OAAO,MAAM,cAAc,YAAY;AAAA,UAChD;AAAA,UACA;AAAA,YACE,OAAO,OAAO,MAAM,cAAc,uBAAuB;AAAA,UAC3D;AAAA,QACF;AAAA,MACF;AACA,YAAM,kBAAkB,CAAC,eAAe,QAAQ;AAAA,QAC9C,WAAW;AAAA,QACX,OAAO,OAAO,MAAM,cAAc,uBAAuB;AAAA,MAC3D;AACA,YAAM,gBAAgB,CAAC,eAAe,QAAQ;AAAA,QAC5C,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO,OAAO,MAAM,cAAc,IAAI;AAAA,QACtC,KAAK;AAAA,MACP;AACA,YAAM,mBAAmB,CAAC,eAAe,QAAQ;AAAA,QAC/C,OAAO,OAAO,cAAc,KAAK;AAAA,QACjC,KAAK,OAAO,OAAO,YAAY;AAAA,QAC/B,UAAU;AAAA,UACR,kBAAkB,YAAY;AAAA,UAC9B,gBAAgB,YAAY;AAAA,UAC5B,cAAc,YAAY;AAAA,QAC5B;AAAA,MACF;AACA,YAAM,qBAAqB,CAAC,eAAe,QAAQ;AAAA,QACjD,OAAO,OAAO,cAAc,GAAG;AAAA,QAC/B,KAAK,OAAO,KAAK,YAAY;AAAA,QAC7B,UAAU;AAAA,UACR,kBAAkB,YAAY;AAAA,UAC9B,cAAc,YAAY;AAAA,QAC5B;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR,iBAAiB;AAAA,UACjB,iBAAiB,GAAG;AAAA,UACpB,iBAAiB,IAAI;AAAA,UACrB,iBAAiB,KAAK;AAAA,UACtB,mBAAmB;AAAA,UACnB,mBAAmB,GAAG;AAAA,UACtB,mBAAmB,IAAI;AAAA,UACvB,mBAAmB,KAAK;AAAA,QAC1B;AAAA,MACF;AAGA,YAAM,oBAAoB;AAAA,QACxB,OAAO,OAAO,KAAK,YAAY,GAAG;AAAA,MACpC;AACA,YAAM,qBAAqB;AAAA,QACzB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,8BAA8B;AAAA,QAClC,WAAW;AAAA,QACX,OAAO,MAAM,mBAAmB;AAAA,MAClC;AACA,YAAM,cAAc;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,sBAAsB;AAAA,QAC1B,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,UACN,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU;AAAA,cACV,UAAU;AAAA,gBACR,GAAG;AAAA,gBACH;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,oBAAoB;AAAA,QACxB,WAAW;AAAA,QACX,OAAO,OAAO,KAAK,OAAO,GAAG,iBAAiB,CAAC;AAAA,MACjD;AACA,YAAM,yBAAyB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO,OAAO,KAAK,UAAU;AAAA,MAC/B;AACA,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,OAAO;AAAA,QACX,OAAO,UAAU,SAAS;AAAA,QAC1B,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,OAAO,iEAAiE,qBAAqB,GAAG;AAAA,UACzG;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YACE,OAAO,OAAO,WAAW,UAAU,cAAc,CAAC;AAAA,YAClD,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,YAAM,oBAAoB;AAAA,QACxB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,WAAK,SAAS,KAAK,iBAAiB;AAIpC,YAAM,qBAAqB;AAAA,QACzB,OAAO,OAAO,YAAY,MAAM;AAAA,QAChC,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAEA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAMA,YAAM,kBAAkB;AAAA,QACtB,eAAe;AAAA,QACf,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO,OAAO,kBAAkB,OAAO,YAAY,QAAQ;AAAA;AAAA;AAAA,YAG3D,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,qBAAqB;AAAA,QACzB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AACA,YAAM,0BAA0B;AAAA,QAC9B,OAAO;AAAA,UACL,UAAU,OAAO,YAAY,MAAM,CAAC;AAAA,UACpC,UAAU,OAAO,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,QACzD;AAAA,QACA,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,sBAAsB;AAAA,QAC1B,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,UACA,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH;AAAA,UACA;AAAA,QACF;AAAA,QACA,YAAY;AAAA,QACZ,SAAS;AAAA,MACX;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO,UAAU,UAAU;AAAA,QAC3B,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAIA,YAAM,iBAAiB;AAAA,QACrB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,UAAU;AAAA,UACR,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAEA,YAAM,uBAAuB;AAAA,QAC3B,eAAe;AAAA,QACf,KAAK,KAAK;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAGA,YAAM,kBAAkB;AAAA,QACtB,eAAe;AAAA,QACf,KAAK,KAAK;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,cACR,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,YACA,UAAU,CAAE,IAAK;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAGA,iBAAW,WAAW,OAAO,UAAU;AACrC,cAAM,gBAAgB,QAAQ,SAAS,KAAK,UAAQ,KAAK,UAAU,UAAU;AAE7E,sBAAc,WAAW;AACzB,cAAM,WAAW;AAAA,UACf,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACL;AACA,sBAAc,WAAW;AAAA,UACvB,GAAG;AAAA,UACH;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,cACA,GAAG;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,UACR,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,UAAU;AAAA,cACR,KAAK,QAAQ,KAAK,YAAY;AAAA,gBAC5B,OAAO;AAAA,cACT,CAAC;AAAA,cACD,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU,CAAE,GAAG,QAAS;AAAA,YACxB,WAAW;AAAA,UACb;AAAA,UACA,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}