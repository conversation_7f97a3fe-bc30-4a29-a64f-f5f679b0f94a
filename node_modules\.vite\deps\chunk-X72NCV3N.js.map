{"version": 3, "sources": ["../../highlight.js/lib/languages/nsis.js"], "sourcesContent": ["/*\nLanguage: NSIS\nDescription: Nullsoft Scriptable Install System\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://nsis.sourceforge.io/Main_Page\n*/\n\nfunction nsis(hljs) {\n  const CONSTANTS = {\n    className: 'variable',\n    begin: /\\$(ADMINTOOLS|APPDATA|CDBURN_AREA|CMDLINE|COMMONFILES32|COMMONFILES64|COMMONFILES|COOKIES|DESKTOP|DOCUMENTS|EXEDIR|EXEFILE|EXEPATH|FAVORITES|FONTS|HISTORY|HWNDPARENT|INSTDIR|INTERNET_CACHE|LANGUAGE|LOCALAPPDATA|MUSIC|NETHOOD|OUTDIR|PICTURES|PLUGINSDIR|PRINTHOOD|PROFILE|PROGRAMFILES32|PROGRAMFILES64|PROGRAMFILES|QUICKLAUNCH|RECENT|RESOURCES_LOCALIZED|RESOURCES|SENDTO|SMPROGRAMS|SMSTARTUP|STARTMENU|SYSDIR|TEMP|TEMPLATES|VIDEOS|WINDIR)/\n  };\n\n  const DEFINES = {\n    // ${defines}\n    className: 'variable',\n    begin: /\\$+\\{[\\w.:-]+\\}/\n  };\n\n  const VARIABLES = {\n    // $variables\n    className: 'variable',\n    begin: /\\$+\\w+/,\n    illegal: /\\(\\)\\{\\}/\n  };\n\n  const LANGUAGES = {\n    // $(language_strings)\n    className: 'variable',\n    begin: /\\$+\\([\\w^.:-]+\\)/\n  };\n\n  const PARAMETERS = {\n    // command parameters\n    className: 'params',\n    begin: '(ARCHIVE|FILE_ATTRIBUTE_ARCHIVE|FILE_ATTRIBUTE_NORMAL|FILE_ATTRIBUTE_OFFLINE|FILE_ATTRIBUTE_READONLY|FILE_ATTRIBUTE_SYSTEM|FILE_ATTRIBUTE_TEMPORARY|HKCR|HKCU|HKDD|HKEY_CLASSES_ROOT|HKEY_CURRENT_CONFIG|HKEY_CURRENT_USER|HKEY_DYN_DATA|HKEY_LOCAL_MACHINE|HKEY_PERFORMANCE_DATA|HKEY_USERS|HKLM|HKPD|HKU|IDABORT|IDCANCEL|IDIGNORE|IDNO|IDOK|IDRETRY|IDYES|MB_ABORTRETRYIGNORE|MB_DEFBUTTON1|MB_DEFBUTTON2|MB_DEFBUTTON3|MB_DEFBUTTON4|MB_ICONEXCLAMATION|MB_ICONINFORMATION|MB_ICONQUESTION|MB_ICONSTOP|MB_OK|MB_OKCANCEL|MB_RETRYCANCEL|MB_RIGHT|MB_RTLREADING|MB_SETFOREGROUND|MB_TOPMOST|MB_USERICON|MB_YESNO|NORMAL|OFFLINE|READONLY|SHCTX|SHELL_CONTEXT|SYSTEM|TEMPORARY)'\n  };\n\n  const COMPILER = {\n    // !compiler_flags\n    className: 'keyword',\n    begin: /!(addincludedir|addplugindir|appendfile|cd|define|delfile|echo|else|endif|error|execute|finalize|getdllversion|gettlbversion|if|ifdef|ifmacrodef|ifmacrondef|ifndef|include|insertmacro|macro|macroend|makensis|packhdr|searchparse|searchreplace|system|tempfile|undef|verbose|warning)/\n  };\n\n  const METACHARS = {\n    // $\\n, $\\r, $\\t, $$\n    className: 'meta',\n    begin: /\\$(\\\\[nrt]|\\$)/\n  };\n\n  const PLUGINS = {\n    // plug::ins\n    className: 'class',\n    begin: /\\w+::\\w+/\n  };\n\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: '\"',\n        end: '\"'\n      },\n      {\n        begin: '\\'',\n        end: '\\''\n      },\n      {\n        begin: '`',\n        end: '`'\n      }\n    ],\n    illegal: /\\n/,\n    contains: [\n      METACHARS,\n      CONSTANTS,\n      DEFINES,\n      VARIABLES,\n      LANGUAGES\n    ]\n  };\n\n  return {\n    name: 'NSIS',\n    case_insensitive: false,\n    keywords: {\n      keyword:\n      'Abort AddBrandingImage AddSize AllowRootDirInstall AllowSkipFiles AutoCloseWindow BGFont BGGradient BrandingText BringToFront Call CallInstDLL Caption ChangeUI CheckBitmap ClearErrors CompletedText ComponentText CopyFiles CRCCheck CreateDirectory CreateFont CreateShortCut Delete DeleteINISec DeleteINIStr DeleteRegKey DeleteRegValue DetailPrint DetailsButtonText DirText DirVar DirVerify EnableWindow EnumRegKey EnumRegValue Exch Exec ExecShell ExecShellWait ExecWait ExpandEnvStrings File FileBufSize FileClose FileErrorText FileOpen FileRead FileReadByte FileReadUTF16LE FileReadWord FileWriteUTF16LE FileSeek FileWrite FileWriteByte FileWriteWord FindClose FindFirst FindNext FindWindow FlushINI GetCurInstType GetCurrentAddress GetDlgItem GetDLLVersion GetDLLVersionLocal GetErrorLevel GetFileTime GetFileTimeLocal GetFullPathName GetFunctionAddress GetInstDirError GetKnownFolderPath GetLabelAddress GetTempFileName Goto HideWindow Icon IfAbort IfErrors IfFileExists IfRebootFlag IfRtlLanguage IfShellVarContextAll IfSilent InitPluginsDir InstallButtonText InstallColors InstallDir InstallDirRegKey InstProgressFlags InstType InstTypeGetText InstTypeSetText Int64Cmp Int64CmpU Int64Fmt IntCmp IntCmpU IntFmt IntOp IntPtrCmp IntPtrCmpU IntPtrOp IsWindow LangString LicenseBkColor LicenseData LicenseForceSelection LicenseLangString LicenseText LoadAndSetImage LoadLanguageFile LockWindow LogSet LogText ManifestDPIAware ManifestLongPathAware ManifestMaxVersionTested ManifestSupportedOS MessageBox MiscButtonText Name Nop OutFile Page PageCallbacks PEAddResource PEDllCharacteristics PERemoveResource PESubsysVer Pop Push Quit ReadEnvStr ReadINIStr ReadRegDWORD ReadRegStr Reboot RegDLL Rename RequestExecutionLevel ReserveFile Return RMDir SearchPath SectionGetFlags SectionGetInstTypes SectionGetSize SectionGetText SectionIn SectionSetFlags SectionSetInstTypes SectionSetSize SectionSetText SendMessage SetAutoClose SetBrandingImage SetCompress SetCompressor SetCompressorDictSize SetCtlColors SetCurInstType SetDatablockOptimize SetDateSave SetDetailsPrint SetDetailsView SetErrorLevel SetErrors SetFileAttributes SetFont SetOutPath SetOverwrite SetRebootFlag SetRegView SetShellVarContext SetSilent ShowInstDetails ShowUninstDetails ShowWindow SilentInstall SilentUnInstall Sleep SpaceTexts StrCmp StrCmpS StrCpy StrLen SubCaption Unicode UninstallButtonText UninstallCaption UninstallIcon UninstallSubCaption UninstallText UninstPage UnRegDLL Var VIAddVersionKey VIFileVersion VIProductVersion WindowIcon WriteINIStr WriteRegBin WriteRegDWORD WriteRegExpandStr WriteRegMultiStr WriteRegNone WriteRegStr WriteUninstaller XPStyle',\n      literal:\n      'admin all auto both bottom bzip2 colored components current custom directory false force hide highest ifdiff ifnewer instfiles lastused leave left license listonly lzma nevershow none normal notset off on open print right show silent silentlog smooth textonly top true try un.components un.custom un.directory un.instfiles un.license uninstConfirm user Win10 Win7 Win8 WinVista zlib'\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.COMMENT(\n        ';',\n        '$',\n        {\n          relevance: 0\n        }\n      ),\n      {\n        className: 'function',\n        beginKeywords: 'Function PageEx Section SectionGroup',\n        end: '$'\n      },\n      STRING,\n      COMPILER,\n      DEFINES,\n      VARIABLES,\n      LANGUAGES,\n      PARAMETERS,\n      PLUGINS,\n      hljs.NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = nsis;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,KAAK,MAAM;AAClB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,UAAU;AAAA;AAAA,QAEd,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,YAAY;AAAA;AAAA,QAEhB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAEA,YAAM,YAAY;AAAA;AAAA,QAEhB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,aAAa;AAAA;AAAA,QAEjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,WAAW;AAAA;AAAA,QAEf,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,YAAY;AAAA;AAAA,QAEhB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,UAAU;AAAA;AAAA,QAEd,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,SACA;AAAA,UACA,SACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,YACb;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}