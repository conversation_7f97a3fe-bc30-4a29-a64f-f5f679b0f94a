import {
  require_wren
} from "./chunk-IOICZARI.js";
import {
  require_xeora
} from "./chunk-OGCBNJIS.js";
import {
  require_xml_doc
} from "./chunk-QVXO72ZT.js";
import {
  require_xojo
} from "./chunk-3BB3GBQ2.js";
import {
  require_xquery
} from "./chunk-3VPXC4GK.js";
import {
  require_yang
} from "./chunk-F52J34YB.js";
import {
  require_zig
} from "./chunk-5OS5KZNL.js";
import {
  require_core
} from "./chunk-RF3N5CBL.js";
import {
  require_vhdl
} from "./chunk-XAYWHNZO.js";
import {
  require_vim
} from "./chunk-UMVVVROE.js";
import {
  require_visual_basic
} from "./chunk-YHO34NDI.js";
import {
  require_warpscript
} from "./chunk-A7BZZQIS.js";
import {
  require_wasm
} from "./chunk-JF6U5RIP.js";
import {
  require_web_idl
} from "./chunk-3BBVUTLX.js";
import {
  require_wiki
} from "./chunk-3TQIDCWA.js";
import {
  require_wolfram
} from "./chunk-CAU23Z6S.js";
import {
  require_typoscript
} from "./chunk-I2U7WM4F.js";
import {
  require_unrealscript
} from "./chunk-6L7TNIDW.js";
import {
  require_uorazor
} from "./chunk-BHRVNT6D.js";
import {
  require_uri
} from "./chunk-ZCLNYR6E.js";
import {
  require_v
} from "./chunk-3HEKUNFL.js";
import {
  require_vala
} from "./chunk-ZSKWCDVC.js";
import {
  require_velocity
} from "./chunk-KL6ACTKC.js";
import {
  require_verilog
} from "./chunk-UPY5AEST.js";
import {
  require_tap
} from "./chunk-LUJIUJTO.js";
import {
  require_tcl
} from "./chunk-RRAMREI4.js";
import {
  require_textile
} from "./chunk-NIWQ3S6K.js";
import {
  require_toml
} from "./chunk-M4DR4SZI.js";
import {
  require_tremor
} from "./chunk-ASX3K5C7.js";
import {
  require_tsx
} from "./chunk-QYPDIX2M.js";
import {
  require_tt2
} from "./chunk-EEKPT3ZJ.js";
import {
  require_twig
} from "./chunk-WTARNKPZ.js";
import {
  require_stylus
} from "./chunk-MVXRR44V.js";
import {
  require_swift
} from "./chunk-IPRGFDDT.js";
import {
  require_systemd
} from "./chunk-U4H5SY5F.js";
import {
  require_t4_cs
} from "./chunk-H555Y6UW.js";
import {
  require_t4_vb
} from "./chunk-NQYUXAQO.js";
import {
  require_t4_templating
} from "./chunk-2J3O23CX.js";
import {
  require_vbnet
} from "./chunk-IMYUT7M6.js";
import {
  require_yaml
} from "./chunk-VUTD2NJZ.js";
import {
  require_solution_file
} from "./chunk-QLTXLPOG.js";
import {
  require_soy
} from "./chunk-TGL2HMA3.js";
import {
  require_sparql
} from "./chunk-TXOV737R.js";
import {
  require_turtle
} from "./chunk-ZNSGDG6O.js";
import {
  require_splunk_spl
} from "./chunk-ZJ44O2HJ.js";
import {
  require_sqf
} from "./chunk-R7GBCYGQ.js";
import {
  require_squirrel
} from "./chunk-EJ6RUZPH.js";
import {
  require_stan
} from "./chunk-3RTUQX6M.js";
import {
  require_scala
} from "./chunk-UPCSETEJ.js";
import {
  require_scss
} from "./chunk-HM3O52X5.js";
import {
  require_shell_session
} from "./chunk-SGCFTYHB.js";
import {
  require_smali
} from "./chunk-Y7OFCAWJ.js";
import {
  require_smalltalk
} from "./chunk-B4WUNITN.js";
import {
  require_smarty
} from "./chunk-D5FLBJR5.js";
import {
  require_sml
} from "./chunk-Q7WZDZLU.js";
import {
  require_solidity
} from "./chunk-P64ALUXN.js";
import {
  require_renpy
} from "./chunk-64F25O5T.js";
import {
  require_rest
} from "./chunk-FMBIMJN7.js";
import {
  require_rip
} from "./chunk-FRHI7LJ4.js";
import {
  require_roboconf
} from "./chunk-ZECRQCRD.js";
import {
  require_robotframework
} from "./chunk-RWCN43VJ.js";
import {
  require_rust
} from "./chunk-3UVGIESI.js";
import {
  require_sas
} from "./chunk-A56ARXXH.js";
import {
  require_sass
} from "./chunk-RCUD47WN.js";
import {
  require_qml
} from "./chunk-R2QMV3K5.js";
import {
  require_qore
} from "./chunk-KEQMWIYP.js";
import {
  require_qsharp
} from "./chunk-XFPXDAWQ.js";
import {
  require_r
} from "./chunk-BHRYARX4.js";
import {
  require_racket
} from "./chunk-YKLQCKAJ.js";
import {
  require_reason
} from "./chunk-65M7XMMD.js";
import {
  require_regex
} from "./chunk-SLV3BBNK.js";
import {
  require_rego
} from "./chunk-AC4UZVMU.js";
import {
  require_psl
} from "./chunk-2Z7SRHF5.js";
import {
  require_pug
} from "./chunk-CXJMRGI3.js";
import {
  require_puppet
} from "./chunk-PB5YUUA4.js";
import {
  require_pure
} from "./chunk-G3RDDKM4.js";
import {
  require_purebasic
} from "./chunk-ODOPV3YQ.js";
import {
  require_purescript
} from "./chunk-YAAJUEGO.js";
import {
  require_python
} from "./chunk-S7JGDWVT.js";
import {
  require_q
} from "./chunk-AUCEL457.js";
import {
  require_plsql
} from "./chunk-HNIPUMFX.js";
import {
  require_powerquery
} from "./chunk-WAFFYVHV.js";
import {
  require_powershell
} from "./chunk-HIQ3NVZT.js";
import {
  require_processing
} from "./chunk-OLDJTZ32.js";
import {
  require_prolog
} from "./chunk-CKL25T7A.js";
import {
  require_promql
} from "./chunk-3QZMU6A2.js";
import {
  require_properties
} from "./chunk-D5ACV7KM.js";
import {
  require_protobuf
} from "./chunk-522HV4CU.js";
import {
  require_parser
} from "./chunk-MDOMCRPB.js";
import {
  require_pascal
} from "./chunk-JDR443OW.js";
import {
  require_pascaligo
} from "./chunk-7Q3YFIMU.js";
import {
  require_pcaxis
} from "./chunk-GFNRUBTV.js";
import {
  require_peoplecode
} from "./chunk-X7PP4Y4V.js";
import {
  require_perl
} from "./chunk-GTJSLXBN.js";
import {
  require_php_extras
} from "./chunk-DCUJM5SD.js";
import {
  require_phpdoc
} from "./chunk-GAYVR2JN.js";
import {
  require_nix
} from "./chunk-MJAE7E72.js";
import {
  require_nsis
} from "./chunk-U3CFTQGF.js";
import {
  require_objectivec
} from "./chunk-LSIMTJYY.js";
import {
  require_ocaml
} from "./chunk-UB7Y3MTF.js";
import {
  require_opencl
} from "./chunk-CEZGFLG5.js";
import {
  require_openqasm
} from "./chunk-DG4K3R2U.js";
import {
  require_oz
} from "./chunk-JIT6QZLY.js";
import {
  require_parigp
} from "./chunk-CJSOVHUU.js";
import {
  require_n4js
} from "./chunk-W5IZEV7R.js";
import {
  require_nand2tetris_hdl
} from "./chunk-ZRXEYDOC.js";
import {
  require_naniscript
} from "./chunk-XCTZJAJJ.js";
import {
  require_nasm
} from "./chunk-EIUDPCN5.js";
import {
  require_neon
} from "./chunk-4EDS2BET.js";
import {
  require_nevod
} from "./chunk-FYX7VPCM.js";
import {
  require_nginx
} from "./chunk-MQGGF4BM.js";
import {
  require_nim
} from "./chunk-2W5SZ5EQ.js";
import {
  require_maxscript
} from "./chunk-FKIHJQVR.js";
import {
  require_mel
} from "./chunk-Q3NFUCIS.js";
import {
  require_mermaid
} from "./chunk-JMUILZ3O.js";
import {
  require_mizar
} from "./chunk-K3JJOSY3.js";
import {
  require_mongodb
} from "./chunk-AL5M4VI5.js";
import {
  require_monkey
} from "./chunk-ZU45UWJ3.js";
import {
  require_moonscript
} from "./chunk-M7CDP5NJ.js";
import {
  require_n1ql
} from "./chunk-OHML5E5M.js";
import {
  require_llvm
} from "./chunk-SQKJXJEO.js";
import {
  require_log
} from "./chunk-KWW7CJON.js";
import {
  require_lolcode
} from "./chunk-XRUE7NGQ.js";
import {
  require_magma
} from "./chunk-ZIM4JAQH.js";
import {
  require_makefile
} from "./chunk-YKQWVAKB.js";
import {
  require_markdown
} from "./chunk-BJBD2BVW.js";
import {
  require_matlab
} from "./chunk-HF73Z2KN.js";
import {
  require_latte
} from "./chunk-LOWEV2DQ.js";
import {
  require_php
} from "./chunk-MOL7FGLB.js";
import {
  require_less
} from "./chunk-WWAJJKVL.js";
import {
  require_lilypond
} from "./chunk-ONLUHWRW.js";
import {
  require_scheme
} from "./chunk-OIRPZ47R.js";
import {
  require_liquid
} from "./chunk-SUCIDXOF.js";
import {
  require_lisp
} from "./chunk-QEFSIJ2K.js";
import {
  require_livescript
} from "./chunk-QINCUVIG.js";
import {
  require_jsx
} from "./chunk-F3QURWWI.js";
import {
  require_julia
} from "./chunk-KNATKPRM.js";
import {
  require_keepalived
} from "./chunk-JOH4C6OF.js";
import {
  require_keyman
} from "./chunk-F4HGTLMG.js";
import {
  require_kotlin
} from "./chunk-ZZJ2JJGI.js";
import {
  require_kumir
} from "./chunk-BWEP2Y22.js";
import {
  require_kusto
} from "./chunk-T72ZIGED.js";
import {
  require_latex
} from "./chunk-5EYK2QD7.js";
import {
  require_js_extras
} from "./chunk-RZGKP2WT.js";
import {
  require_js_templates
} from "./chunk-SA4C6OJT.js";
import {
  require_jsdoc
} from "./chunk-AMTWYXKI.js";
import {
  require_typescript
} from "./chunk-67KFXUWO.js";
import {
  require_json5
} from "./chunk-VGLSEAFS.js";
import {
  require_jsonp
} from "./chunk-2V54CENR.js";
import {
  require_json
} from "./chunk-T6HOABKV.js";
import {
  require_jsstacktrace
} from "./chunk-2B6VIOUH.js";
import {
  require_javadoc
} from "./chunk-CSCKTNOS.js";
import {
  require_java
} from "./chunk-KY7O4OXK.js";
import {
  require_javadoclike
} from "./chunk-66ESUJ6L.js";
import {
  require_javastacktrace
} from "./chunk-CYTUDLOP.js";
import {
  require_jexl
} from "./chunk-XPW5REWX.js";
import {
  require_jolie
} from "./chunk-XWCJZFWE.js";
import {
  require_jq
} from "./chunk-AAFZP24Q.js";
import {
  require_icu_message_format
} from "./chunk-67RSGS24.js";
import {
  require_idris
} from "./chunk-ZVAJC64O.js";
import {
  require_iecst
} from "./chunk-KLDZBBUW.js";
import {
  require_ignore
} from "./chunk-CUGNAT3I.js";
import {
  require_inform7
} from "./chunk-Q2A7G3YG.js";
import {
  require_ini
} from "./chunk-UUCGPL6T.js";
import {
  require_io
} from "./chunk-AGA2MGHX.js";
import {
  require_j
} from "./chunk-AO7TZKVZ.js";
import {
  require_hcl
} from "./chunk-WFOEC5QZ.js";
import {
  require_hlsl
} from "./chunk-CBFIIZXL.js";
import {
  require_hoon
} from "./chunk-WXNEWAOC.js";
import {
  require_hpkp
} from "./chunk-KT54FML6.js";
import {
  require_hsts
} from "./chunk-6HUHKCOV.js";
import {
  require_http
} from "./chunk-GELY4XMD.js";
import {
  require_ichigojam
} from "./chunk-JFDHUMA5.js";
import {
  require_icon
} from "./chunk-IHYI374P.js";
import {
  require_go_module
} from "./chunk-R6QXLGQC.js";
import {
  require_go
} from "./chunk-DQCONQRX.js";
import {
  require_graphql
} from "./chunk-JHVDAPJ3.js";
import {
  require_groovy
} from "./chunk-RVNST3R5.js";
import {
  require_haml
} from "./chunk-WYPTA6RZ.js";
import {
  require_handlebars
} from "./chunk-3TA3BC2C.js";
import {
  require_haskell
} from "./chunk-VSR6JGP3.js";
import {
  require_haxe
} from "./chunk-VBPCL4DR.js";
import {
  require_gcode
} from "./chunk-EBPBD6W2.js";
import {
  require_gdscript
} from "./chunk-3JNKYVZX.js";
import {
  require_gedcom
} from "./chunk-7ZVGHWF4.js";
import {
  require_gherkin
} from "./chunk-OSEMK3WT.js";
import {
  require_git
} from "./chunk-GJNHKHOH.js";
import {
  require_glsl
} from "./chunk-HEY3XCZV.js";
import {
  require_gml
} from "./chunk-2I3JBDZ5.js";
import {
  require_gn
} from "./chunk-OHA3KVPR.js";
import {
  require_factor
} from "./chunk-NJ2EDKTO.js";
import {
  require_false
} from "./chunk-X6CRM4N4.js";
import {
  require_firestore_security_rules
} from "./chunk-MHWQRVTF.js";
import {
  require_flow
} from "./chunk-VQCXPFPG.js";
import {
  require_fortran
} from "./chunk-CSCUYJT4.js";
import {
  require_fsharp
} from "./chunk-L7JSRRNL.js";
import {
  require_ftl
} from "./chunk-XPHBBGRY.js";
import {
  require_gap
} from "./chunk-2WTQN4AP.js";
import {
  require_ejs
} from "./chunk-GCKZAEMB.js";
import {
  require_elixir
} from "./chunk-735334YK.js";
import {
  require_elm
} from "./chunk-MT3P7GGY.js";
import {
  require_erb
} from "./chunk-5XPJIK6M.js";
import {
  require_erlang
} from "./chunk-X5TGCJAK.js";
import {
  require_etlua
} from "./chunk-O2BZIRUI.js";
import {
  require_lua
} from "./chunk-45RJK632.js";
import {
  require_excel_formula
} from "./chunk-5RO5QRBD.js";
import {
  require_django
} from "./chunk-6HMNQV62.js";
import {
  require_markup_templating
} from "./chunk-HZQQIYRC.js";
import {
  require_dns_zone_file
} from "./chunk-64ZEWP6E.js";
import {
  require_docker
} from "./chunk-JNLFAQUQ.js";
import {
  require_dot
} from "./chunk-TPGKVD3F.js";
import {
  require_ebnf
} from "./chunk-HIXF7L52.js";
import {
  require_editorconfig
} from "./chunk-CF27GDQP.js";
import {
  require_eiffel
} from "./chunk-YPDE5XED.js";
import {
  require_csv
} from "./chunk-7MBVJ7MP.js";
import {
  require_cypher
} from "./chunk-77P6O7Y6.js";
import {
  require_d
} from "./chunk-L4GXFL6O.js";
import {
  require_dart
} from "./chunk-GFDRFWET.js";
import {
  require_dataweave
} from "./chunk-Q37I7RED.js";
import {
  require_dax
} from "./chunk-PCP2KOWZ.js";
import {
  require_dhall
} from "./chunk-W57IGYGA.js";
import {
  require_diff
} from "./chunk-54KRQEYO.js";
import {
  require_concurnas
} from "./chunk-WILDCF73.js";
import {
  require_coq
} from "./chunk-6QNMXIIF.js";
import {
  require_crystal
} from "./chunk-Y7UILQVS.js";
import {
  require_ruby
} from "./chunk-5ABY4UTV.js";
import {
  require_cshtml
} from "./chunk-VRG5RLUN.js";
import {
  require_csp
} from "./chunk-AYZPVQTW.js";
import {
  require_css_extras
} from "./chunk-MCQIZL4P.js";
import {
  require_cfscript
} from "./chunk-NRRURLDJ.js";
import {
  require_chaiscript
} from "./chunk-AEEBC6LV.js";
import {
  require_cil
} from "./chunk-EKSMSNVP.js";
import {
  require_clojure
} from "./chunk-ULC5ZUQQ.js";
import {
  require_cmake
} from "./chunk-2BYRCPFF.js";
import {
  require_cobol
} from "./chunk-O763PIET.js";
import {
  require_coffeescript
} from "./chunk-II4DA3AG.js";
import {
  require_bicep
} from "./chunk-TGI3YYWP.js";
import {
  require_birb
} from "./chunk-3XVC26GY.js";
import {
  require_bison
} from "./chunk-POP5VGLG.js";
import {
  require_bnf
} from "./chunk-VA5PKY37.js";
import {
  require_brainfuck
} from "./chunk-QMUYEIFN.js";
import {
  require_brightscript
} from "./chunk-YHQJB3U5.js";
import {
  require_bro
} from "./chunk-3PFERXQ5.js";
import {
  require_bsl
} from "./chunk-4QZACVDM.js";
import {
  require_autohotkey
} from "./chunk-ZAJQNV3P.js";
import {
  require_autoit
} from "./chunk-SA4GV5HL.js";
import {
  require_avisynth
} from "./chunk-H6FYCVNR.js";
import {
  require_avro_idl
} from "./chunk-GTX6Z5WH.js";
import {
  require_bash
} from "./chunk-OJJV55KR.js";
import {
  require_basic
} from "./chunk-DDVED2AS.js";
import {
  require_batch
} from "./chunk-H6XBWFM5.js";
import {
  require_bbcode
} from "./chunk-VL6WVQ4F.js";
import {
  require_arduino
} from "./chunk-SFD6YX65.js";
import {
  require_cpp
} from "./chunk-LZ5CKOX2.js";
import {
  require_arff
} from "./chunk-XSESVUIR.js";
import {
  require_asciidoc
} from "./chunk-FHAWJOO4.js";
import {
  require_asm6502
} from "./chunk-5I4OUDU5.js";
import {
  require_asmatmel
} from "./chunk-V4YSHKFU.js";
import {
  require_aspnet
} from "./chunk-NJ7UI7GR.js";
import {
  require_csharp
} from "./chunk-SGM4KGGR.js";
import {
  require_antlr4
} from "./chunk-PKYBCATR.js";
import {
  require_apacheconf
} from "./chunk-XLNZVNVA.js";
import {
  require_apex
} from "./chunk-Y7RTRQE2.js";
import {
  require_sql
} from "./chunk-VOCETDAA.js";
import {
  require_apl
} from "./chunk-AFVBVPIL.js";
import {
  require_applescript
} from "./chunk-LC24DCLT.js";
import {
  require_aql
} from "./chunk-VMPJADZ5.js";
import {
  require_c
} from "./chunk-PEZKOG3B.js";
import {
  require_abap
} from "./chunk-PXI6RTK4.js";
import {
  require_abnf
} from "./chunk-CT3QSIZF.js";
import {
  require_actionscript
} from "./chunk-LLTUN3HQ.js";
import {
  require_ada
} from "./chunk-TJNC4PGA.js";
import {
  require_agda
} from "./chunk-E5YKVOOD.js";
import {
  require_al
} from "./chunk-QSS4MEXO.js";
import {
  __commonJS
} from "./chunk-2TUXWMP5.js";

// node_modules/refractor/index.js
var require_refractor = __commonJS({
  "node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-EX7OJBQZ.js.map
