import { useEffect, useState } from 'react';
import {
  getAllPosts,
  getAllProjects,
  getFeaturedPosts,
  getFeaturedProjects,
  getPostBySlug,
} from '../utils/content.js';

/**
 * Hook para gerenciar posts do blog
 * @returns {Object} Estado dos posts e métodos
 */
export function usePosts() {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchPosts() {
      try {
        setLoading(true);
        const allPosts = await getAllPosts();
        setPosts(allPosts);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchPosts();
  }, []);

  return { posts, loading, error };
}

/**
 * Hook para obter um post específico
 * @param {string} slug - Slug do post
 * @returns {Object} Estado do post e métodos
 */
export function usePost(slug) {
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchPost() {
      if (!slug) return;

      try {
        setLoading(true);
        const postData = await getPostBySlug(slug);
        setPost(postData);

        if (!postData) {
          setError('Post não encontrado');
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchPost();
  }, [slug]);

  return { post, loading, error };
}

/**
 * Hook para obter posts em destaque
 * @param {number} limit - Número de posts para buscar
 * @returns {Object} Estado dos posts em destaque
 */
export function useFeaturedPosts(limit = 3) {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchFeaturedPosts() {
      try {
        setLoading(true);
        const featuredPosts = await getFeaturedPosts(limit);
        setPosts(featuredPosts);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchFeaturedPosts();
  }, [limit]);

  return { posts, loading, error };
}

/**
 * Hook para gerenciar projetos
 * @returns {Object} Estado dos projetos e métodos
 */
export function useProjects() {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchProjects() {
      try {
        setLoading(true);
        const allProjects = await getAllProjects();
        setProjects(allProjects);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchProjects();
  }, []);

  return { projects, loading, error };
}

/**
 * Hook para obter projetos em destaque
 * @param {number} limit - Número de projetos para buscar
 * @returns {Object} Estado dos projetos em destaque
 */
export function useFeaturedProjects(limit = 3) {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchFeaturedProjects() {
      try {
        setLoading(true);
        const featuredProjects = await getFeaturedProjects(limit);
        setProjects(featuredProjects);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchFeaturedProjects();
  }, [limit]);

  return { projects, loading, error };
}
