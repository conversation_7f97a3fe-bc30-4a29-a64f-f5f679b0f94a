import { useEffect, useState } from 'react';
import {
  getAllPosts,
  getAllProjects,
  getFeaturedPosts,
  getFeaturedProjects,
  getPostBySlug,
} from '../utils/content.js';

/**
 * Hook para gerenciar posts do blog
 * @returns {Object} Estado dos posts e métodos
 */
export function usePosts() {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchPosts() {
      try {
        setLoading(true);
        const allPosts = await getAllPosts();
        setPosts(allPosts);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchPosts();
  }, []);

  return { posts, loading, error };
}

/**
 * Hook para obter um post específico
 * @param {string} slug - Slug do post
 * @returns {Object} Estado do post e métodos
 */
export function usePost(slug) {
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchPost() {
      if (!slug) return;

      try {
        setLoading(true);
        const postData = await getPostBySlug(slug);
        setPost(postData);

        if (!postData) {
          setError('Post não encontrado');
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchPost();
  }, [slug]);

  return { post, loading, error };
}

/**
 * Hook para obter posts em destaque
 * @param {number} limit - Número de posts para buscar
 * @returns {Object} Estado dos posts em destaque
 */
export function useFeaturedPosts(limit = 3) {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchFeaturedPosts() {
      try {
        setLoading(true);
        const featuredPosts = await getFeaturedPosts(limit);
        setPosts(featuredPosts);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchFeaturedPosts();
  }, [limit]);

  return { posts, loading, error };
}

/**
 * Hook para gerenciar projetos
 * @returns {Object} Estado dos projetos e métodos
 */
export function useProjects() {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchProjects() {
      try {
        setLoading(true);
        const allProjects = await getAllProjects();
        setProjects(allProjects);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchProjects();
  }, []);

  return { projects, loading, error };
}

/**
 * Hook para obter projetos em destaque
 * @param {number} limit - Número de projetos para buscar
 * @returns {Object} Estado dos projetos em destaque
 */
export function useFeaturedProjects(limit = 3) {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchFeaturedProjects() {
      try {
        setLoading(true);
        const featuredProjects = await getFeaturedProjects(limit);
        setProjects(featuredProjects);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchFeaturedProjects();
  }, [limit]);

  return { projects, loading, error };
}

/**
 * Hook para gerenciar devlogs
 * @returns {Object} Estado dos devlogs e métodos
 */
export function useDevlogs() {
  const [devlogs, setDevlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchDevlogs() {
      try {
        setLoading(true);
        const { getAllDevlogs } = await import('../utils/content.js');
        const allDevlogs = await getAllDevlogs();
        setDevlogs(allDevlogs);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchDevlogs();
  }, []);

  return { devlogs, loading, error };
}

/**
 * Hook para obter um devlog específico
 * @param {string} id - ID do devlog
 * @returns {Object} Estado do devlog e métodos
 */
export function useDevlog(id) {
  const [devlog, setDevlog] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchDevlog() {
      if (!id) return;

      try {
        setLoading(true);
        const { getDevlogById } = await import('../utils/content.js');
        const devlogData = await getDevlogById(id);
        setDevlog(devlogData);

        if (!devlogData) {
          setError('Devlog não encontrado');
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchDevlog();
  }, [id]);

  return { devlog, loading, error };
}

/**
 * Hook para obter devlogs recentes
 * @param {number} limit - Número de devlogs para buscar
 * @returns {Object} Estado dos devlogs recentes
 */
export function useRecentDevlogs(limit = 5) {
  const [devlogs, setDevlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchRecentDevlogs() {
      try {
        setLoading(true);
        const { getRecentDevlogs } = await import('../utils/content.js');
        const recentDevlogs = await getRecentDevlogs(limit);
        setDevlogs(recentDevlogs);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchRecentDevlogs();
  }, [limit]);

  return { devlogs, loading, error };
}
