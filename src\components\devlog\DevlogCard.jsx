import { Calendar, Clock, Code2, GitBranch, Lightbulb, Target } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

function DevlogCard({ devlog, featured = false }) {
  const getTypeIcon = (type) => {
    switch (type) {
      case 'milestone':
        return <Target className="h-5 w-5" />;
      case 'feature':
        return <Code2 className="h-5 w-5" />;
      case 'bugfix':
        return <GitBranch className="h-5 w-5" />;
      case 'update':
        return <Lightbulb className="h-5 w-5" />;
      default:
        return <Code2 className="h-5 w-5" />;
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'milestone':
        return 'Marco';
      case 'feature':
        return 'Feature';
      case 'bugfix':
        return 'Bugfix';
      case 'update':
        return 'Update';
      default:
        return type;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'milestone':
        return 'text-cyber-purple bg-cyber-purple/10 border-cyber-purple/20';
      case 'feature':
        return 'text-neon-green bg-neon-green/10 border-neon-green/20';
      case 'bugfix':
        return 'text-red-400 bg-red-400/10 border-red-400/20';
      case 'update':
        return 'text-neon-blue bg-neon-blue/10 border-neon-blue/20';
      default:
        return 'text-dark-text-secondary bg-dark-border/10 border-dark-border/20';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-neon-green bg-neon-green/10 border-neon-green/20';
      case 'in-progress':
        return 'text-neon-blue bg-neon-blue/10 border-neon-blue/20';
      case 'paused':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      default:
        return 'text-dark-text-secondary bg-dark-border/10 border-dark-border/20';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'completed':
        return 'Concluído';
      case 'in-progress':
        return 'Em progresso';
      case 'paused':
        return 'Pausado';
      default:
        return status;
    }
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy':
        return 'text-neon-green';
      case 'medium':
        return 'text-yellow-400';
      case 'hard':
        return 'text-red-400';
      default:
        return 'text-dark-text-secondary';
    }
  };

  const getDifficultyLabel = (difficulty) => {
    switch (difficulty) {
      case 'easy':
        return 'Fácil';
      case 'medium':
        return 'Médio';
      case 'hard':
        return 'Difícil';
      default:
        return difficulty;
    }
  };

  const formattedDate = format(new Date(devlog.date), "dd 'de' MMM", { locale: ptBR });

  return (
    <div className={`card group hover:border-neon-green/30 transition-all duration-300 ${
      featured ? 'ring-1 ring-neon-green/20' : ''
    }`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg border ${getTypeColor(devlog.type)}`}>
            {getTypeIcon(devlog.type)}
          </div>
          <div>
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 text-xs font-medium rounded border ${getTypeColor(devlog.type)}`}>
                {getTypeLabel(devlog.type)}
              </span>
              <span className={`px-2 py-1 text-xs font-medium rounded border ${getStatusColor(devlog.status)}`}>
                {getStatusLabel(devlog.status)}
              </span>
            </div>
          </div>
        </div>
        
        {featured && (
          <span className="px-2 py-1 text-xs font-medium text-neon-green bg-neon-green/10 border border-neon-green/20 rounded">
            Destaque
          </span>
        )}
      </div>

      {/* Content */}
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gradient mb-2 group-hover:text-neon-green transition-colors">
          {devlog.title}
        </h3>
        <p className="text-dark-text-secondary text-sm leading-relaxed">
          {devlog.description}
        </p>
      </div>

      {/* Project */}
      <div className="mb-4">
        <span className="text-xs text-dark-text-secondary">Projeto:</span>
        <span className="ml-2 text-sm font-medium text-neon-blue">
          {devlog.project}
        </span>
      </div>

      {/* Metadata */}
      <div className="flex flex-wrap items-center gap-4 text-xs text-dark-text-secondary mb-4">
        <div className="flex items-center space-x-1">
          <Calendar className="h-3 w-3" />
          <span>{formattedDate}</span>
        </div>
        
        {devlog.timeSpent && (
          <div className="flex items-center space-x-1">
            <Clock className="h-3 w-3" />
            <span>{devlog.timeSpent}</span>
          </div>
        )}
        
        {devlog.difficulty && (
          <div className="flex items-center space-x-1">
            <span className={getDifficultyColor(devlog.difficulty)}>●</span>
            <span>{getDifficultyLabel(devlog.difficulty)}</span>
          </div>
        )}
      </div>

      {/* Tags */}
      {devlog.tags && devlog.tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {devlog.tags.map((tag) => (
            <span
              key={tag}
              className="px-2 py-1 text-xs bg-dark-border/20 text-dark-text-secondary rounded hover:bg-neon-green/10 hover:text-neon-green transition-colors"
            >
              #{tag}
            </span>
          ))}
        </div>
      )}

      {/* Learnings Preview */}
      {devlog.learnings && devlog.learnings.length > 0 && (
        <div className="mb-4">
          <h4 className="text-xs font-medium text-dark-text-secondary mb-2">💡 Aprendizados:</h4>
          <ul className="text-xs text-dark-text-secondary space-y-1">
            {devlog.learnings.slice(0, 2).map((learning, index) => (
              <li key={index} className="flex items-start space-x-2">
                <span className="text-neon-green mt-0.5">•</span>
                <span>{learning}</span>
              </li>
            ))}
            {devlog.learnings.length > 2 && (
              <li className="text-dark-text-secondary/60">
                +{devlog.learnings.length - 2} mais...
              </li>
            )}
          </ul>
        </div>
      )}

      {/* Links */}
      {devlog.links && devlog.links.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {devlog.links.map((link, index) => (
            <a
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-neon-blue hover:text-neon-green transition-colors underline"
            >
              {link.type === 'github' ? '🔗 GitHub' : 
               link.type === 'demo' ? '🚀 Demo' : 
               '🔗 Link'}
            </a>
          ))}
        </div>
      )}
    </div>
  );
}

export default DevlogCard;
