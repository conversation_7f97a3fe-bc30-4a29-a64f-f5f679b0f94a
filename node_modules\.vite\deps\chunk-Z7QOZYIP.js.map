{"version": 3, "sources": ["../../highlight.js/lib/languages/llvm.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: LLVM IR\nAuthor: <PERSON> <<EMAIL>>\nDescription: language used as intermediate representation in the LLVM compiler framework\nWebsite: https://llvm.org/docs/LangRef.html\nCategory: assembler\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction llvm(hljs) {\n  const IDENT_RE = /([-a-zA-Z$._][\\w$.-]*)/;\n  const TYPE = {\n    className: 'type',\n    begin: /\\bi\\d+(?=\\s|\\b)/\n  };\n  const OPERATOR = {\n    className: 'operator',\n    relevance: 0,\n    begin: /=/\n  };\n  const PUNCTUATION = {\n    className: 'punctuation',\n    relevance: 0,\n    begin: /,/\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [\n        { begin: /0[xX][a-fA-F0-9]+/ },\n        { begin: /-?\\d+(?:[.]\\d+)?(?:[eE][-+]?\\d+(?:[.]\\d+)?)?/ }\n    ],\n    relevance: 0\n  };\n  const LABEL = {\n    className: 'symbol',\n    variants: [\n        { begin: /^\\s*[a-z]+:/ }, // labels\n    ],\n    relevance: 0\n  };\n  const VARIABLE = {\n    className: 'variable',\n    variants: [\n      { begin: concat(/%/, IDENT_RE) },\n      { begin: /%\\d+/ },\n      { begin: /#\\d+/ },\n    ]\n  };\n  const FUNCTION = {\n    className: 'title',\n    variants: [\n      { begin: concat(/@/, IDENT_RE) },\n      { begin: /@\\d+/ },\n      { begin: concat(/!/, IDENT_RE) },\n      { begin: concat(/!\\d+/, IDENT_RE) },\n      // https://llvm.org/docs/LangRef.html#namedmetadatastructure\n      // obviously a single digit can also be used in this fashion\n      { begin: /!\\d+/ }\n    ]\n  };\n\n  return {\n    name: 'LLVM IR',\n    // TODO: split into different categories of keywords\n    keywords:\n      'begin end true false declare define global ' +\n      'constant private linker_private internal ' +\n      'available_externally linkonce linkonce_odr weak ' +\n      'weak_odr appending dllimport dllexport common ' +\n      'default hidden protected extern_weak external ' +\n      'thread_local zeroinitializer undef null to tail ' +\n      'target triple datalayout volatile nuw nsw nnan ' +\n      'ninf nsz arcp fast exact inbounds align ' +\n      'addrspace section alias module asm sideeffect ' +\n      'gc dbg linker_private_weak attributes blockaddress ' +\n      'initialexec localdynamic localexec prefix unnamed_addr ' +\n      'ccc fastcc coldcc x86_stdcallcc x86_fastcallcc ' +\n      'arm_apcscc arm_aapcscc arm_aapcs_vfpcc ptx_device ' +\n      'ptx_kernel intel_ocl_bicc msp430_intrcc spir_func ' +\n      'spir_kernel x86_64_sysvcc x86_64_win64cc x86_thiscallcc ' +\n      'cc c signext zeroext inreg sret nounwind ' +\n      'noreturn noalias nocapture byval nest readnone ' +\n      'readonly inlinehint noinline alwaysinline optsize ssp ' +\n      'sspreq noredzone noimplicitfloat naked builtin cold ' +\n      'nobuiltin noduplicate nonlazybind optnone returns_twice ' +\n      'sanitize_address sanitize_memory sanitize_thread sspstrong ' +\n      'uwtable returned type opaque eq ne slt sgt ' +\n      'sle sge ult ugt ule uge oeq one olt ogt ' +\n      'ole oge ord uno ueq une x acq_rel acquire ' +\n      'alignstack atomic catch cleanup filter inteldialect ' +\n      'max min monotonic nand personality release seq_cst ' +\n      'singlethread umax umin unordered xchg add fadd ' +\n      'sub fsub mul fmul udiv sdiv fdiv urem srem ' +\n      'frem shl lshr ashr and or xor icmp fcmp ' +\n      'phi call trunc zext sext fptrunc fpext uitofp ' +\n      'sitofp fptoui fptosi inttoptr ptrtoint bitcast ' +\n      'addrspacecast select va_arg ret br switch invoke ' +\n      'unwind unreachable indirectbr landingpad resume ' +\n      'malloc alloca free load store getelementptr ' +\n      'extractelement insertelement shufflevector getresult ' +\n      'extractvalue insertvalue atomicrmw cmpxchg fence ' +\n      'argmemonly double',\n    contains: [\n      TYPE,\n      // this matches \"empty comments\"...\n      // ...because it's far more likely this is a statement terminator in\n      // another language than an actual comment\n      hljs.COMMENT(/;\\s*$/, null, { relevance: 0 }),\n      hljs.COMMENT(/;/, /$/),\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        variants: [\n          // Double-quoted string\n          { begin: /\"/, end: /[^\\\\]\"/ },\n        ]\n      },\n      FUNCTION,\n      PUNCTUATION,\n      OPERATOR,\n      VARIABLE,\n      LABEL,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = llvm;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAYA,aAAS,KAAK,MAAM;AAClB,YAAM,WAAW;AACjB,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACN,EAAE,OAAO,oBAAoB;AAAA,UAC7B,EAAE,OAAO,+CAA+C;AAAA,QAC5D;AAAA,QACA,WAAW;AAAA,MACb;AACA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,UACN,EAAE,OAAO,cAAc;AAAA;AAAA,QAC3B;AAAA,QACA,WAAW;AAAA,MACb;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,OAAO,KAAK,QAAQ,EAAE;AAAA,UAC/B,EAAE,OAAO,OAAO;AAAA,UAChB,EAAE,OAAO,OAAO;AAAA,QAClB;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,OAAO,KAAK,QAAQ,EAAE;AAAA,UAC/B,EAAE,OAAO,OAAO;AAAA,UAChB,EAAE,OAAO,OAAO,KAAK,QAAQ,EAAE;AAAA,UAC/B,EAAE,OAAO,OAAO,QAAQ,QAAQ,EAAE;AAAA;AAAA;AAAA,UAGlC,EAAE,OAAO,OAAO;AAAA,QAClB;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA;AAAA,QAEN,UACE;AAAA,QAqCF,UAAU;AAAA,UACR;AAAA;AAAA;AAAA;AAAA,UAIA,KAAK,QAAQ,SAAS,MAAM,EAAE,WAAW,EAAE,CAAC;AAAA,UAC5C,KAAK,QAAQ,KAAK,GAAG;AAAA,UACrB,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA;AAAA,cAER,EAAE,OAAO,KAAK,KAAK,SAAS;AAAA,YAC9B;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}