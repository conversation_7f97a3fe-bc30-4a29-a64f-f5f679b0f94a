{"version": 3, "sources": ["../../fault/index.js", "../../micromark-extension-frontmatter/dev/lib/to-matters.js", "../../micromark-extension-frontmatter/dev/lib/syntax.js", "../../mdast-util-frontmatter/node_modules/escape-string-regexp/index.js", "../../mdast-util-frontmatter/lib/index.js", "../../remark-frontmatter/lib/index.js"], "sourcesContent": ["// @ts-expect-error\nimport formatter from 'format'\n\nexport const fault = Object.assign(create(Error), {\n  eval: create(EvalError),\n  range: create(RangeError),\n  reference: create(ReferenceError),\n  syntax: create(SyntaxError),\n  type: create(TypeError),\n  uri: create(URIError)\n})\n\n/**\n * Create a new `EConstructor`, with the formatted `format` as a first argument.\n *\n * @template {Error} Fault\n * @template {new (reason: string) => Fault} Class\n * @param {Class} Constructor\n */\nexport function create(Constructor) {\n  /** @type {string} */\n  // @ts-expect-error\n  FormattedError.displayName = Constructor.displayName || Constructor.name\n\n  return FormattedError\n\n  /**\n   * Create an error with a printf-like formatted message.\n   *\n   * @param {string|null} [format]\n   *   Template string.\n   * @param {...unknown} values\n   *   Values to render in `format`.\n   * @returns {Fault}\n   */\n  function FormattedError(format, ...values) {\n    /** @type {string} */\n    const reason = format ? formatter(format, ...values) : format\n    return new Constructor(reason)\n  }\n}\n", "/**\n * @typedef {'toml' | 'yaml'} Preset\n *   Known name of a frontmatter style.\n *\n * @typedef Info\n *   Sequence.\n *\n *   Depending on how this structure is used, it reflects a marker or a fence.\n * @property {string} close\n *   Closing.\n * @property {string} open\n *   Opening.\n *\n * @typedef MatterProps\n *   Fields describing a kind of matter.\n * @property {string} type\n *   Node type to tokenize as.\n * @property {boolean | null | undefined} [anywhere=false]\n *   Whether matter can be found anywhere in the document, normally, only matter\n *   at the start of the document is recognized.\n *\n *   > 👉 **Note**: using this is a terrible idea.\n *   > It’s called frontmatter, not matter-in-the-middle or so.\n *   > This makes your markdown less portable.\n *\n * @typedef MarkerProps\n *   Marker configuration.\n * @property {Info | string} marker\n *   Character repeated 3 times, used as complete fences.\n *\n *   For example the character `'-'` will result in `'---'` being used as the\n *   fence\n *   Pass `open` and `close` to specify different characters for opening and\n *   closing fences.\n * @property {never} [fence]\n *   If `marker` is set, `fence` must not be set.\n *\n * @typedef FenceProps\n *   Fence configuration.\n * @property {Info | string} fence\n *   Complete fences.\n *\n *   This can be used when fences contain different characters or lengths\n *   other than 3.\n *   Pass `open` and `close` to interface to specify different characters for opening and\n *   closing fences.\n * @property {never} [marker]\n *   If `fence` is set, `marker` must not be set.\n *\n * @typedef {(MatterProps & FenceProps) | (MatterProps & MarkerProps)} Matter\n *   Fields describing a kind of matter.\n *\n *   > 👉 **Note**: using `anywhere` is a terrible idea.\n *   > It’s called frontmatter, not matter-in-the-middle or so.\n *   > This makes your markdown less portable.\n *\n *   > 👉 **Note**: `marker` and `fence` are mutually exclusive.\n *   > If `marker` is set, `fence` must not be set, and vice versa.\n *\n * @typedef {Matter | Preset | Array<Matter | Preset>} Options\n *   Configuration.\n */\n\nimport {fault} from 'fault'\n\nconst own = {}.hasOwnProperty\nconst markers = {yaml: '-', toml: '+'}\n\n/**\n * Simplify options by normalizing them to an array of matters.\n *\n * @param {Options | null | undefined} [options='yaml']\n *   Configuration (default: `'yaml'`).\n * @returns {Array<Matter>}\n *   List of matters.\n */\nexport function toMatters(options) {\n  /** @type {Array<Matter>} */\n  const result = []\n  let index = -1\n\n  /** @type {Array<Matter | Preset>} */\n  const presetsOrMatters = Array.isArray(options)\n    ? options\n    : options\n    ? [options]\n    : ['yaml']\n\n  while (++index < presetsOrMatters.length) {\n    result[index] = matter(presetsOrMatters[index])\n  }\n\n  return result\n}\n\n/**\n * Simplify an option.\n *\n * @param {Matter | Preset} option\n *   Configuration.\n * @returns {Matter}\n *   Matter.\n */\nfunction matter(option) {\n  let result = option\n\n  if (typeof result === 'string') {\n    if (!own.call(markers, result)) {\n      throw fault('Missing matter definition for `%s`', result)\n    }\n\n    result = {type: result, marker: markers[result]}\n  } else if (typeof result !== 'object') {\n    throw fault('Expected matter to be an object, not `%j`', result)\n  }\n\n  if (!own.call(result, 'type')) {\n    throw fault('Missing `type` in matter `%j`', result)\n  }\n\n  if (!own.call(result, 'fence') && !own.call(result, 'marker')) {\n    throw fault('Missing `marker` or `fence` in matter `%j`', result)\n  }\n\n  return result\n}\n", "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').ConstructRecord} ConstructRecord\n * @typedef {import('micromark-util-types').Extension} Extension\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenType} TokenType\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n *\n * @typedef {import('./to-matters.js').Info} Info\n * @typedef {import('./to-matters.js').Matter} Matter\n * @typedef {import('./to-matters.js').Options} Options\n */\n\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\nimport {toMatters} from './to-matters.js'\n\n/**\n * Create an extension for `micromark` to enable frontmatter syntax.\n *\n * @param {Options | null | undefined} [options='yaml']\n *   Configuration (default: `'yaml'`).\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions`, to\n *   enable frontmatter syntax.\n */\nexport function frontmatter(options) {\n  const matters = toMatters(options)\n  /** @type {ConstructRecord} */\n  const flow = {}\n  let index = -1\n\n  while (++index < matters.length) {\n    const matter = matters[index]\n    const code = fence(matter, 'open').charCodeAt(0)\n    const construct = createConstruct(matter)\n    const existing = flow[code]\n\n    if (Array.isArray(existing)) {\n      existing.push(construct)\n    } else {\n      // Never a single object, always an array.\n      flow[code] = [construct]\n    }\n  }\n\n  return {flow}\n}\n\n/**\n * @param {Matter} matter\n * @returns {Construct}\n */\nfunction createConstruct(matter) {\n  const anywhere = matter.anywhere\n  const frontmatterType = /** @type {TokenType} */ (matter.type)\n  const fenceType = /** @type {TokenType} */ (frontmatterType + 'Fence')\n  const sequenceType = /** @type {TokenType} */ (fenceType + 'Sequence')\n  const valueType = /** @type {TokenType} */ (frontmatterType + 'Value')\n  const closingFenceConstruct = {tokenize: tokenizeClosingFence, partial: true}\n\n  /**\n   * Fence to look for.\n   *\n   * @type {string}\n   */\n  let buffer\n  let bufferIndex = 0\n\n  return {tokenize: tokenizeFrontmatter, concrete: true}\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeFrontmatter(effects, ok, nok) {\n    const self = this\n\n    return start\n\n    /**\n     * Start of frontmatter.\n     *\n     * ```markdown\n     * > | ---\n     *     ^\n     *   | title: \"Venus\"\n     *   | ---\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      const position = self.now()\n\n      if (\n        // Indent not allowed.\n        position.column === 1 &&\n        // Normally, only allowed in first line.\n        (position.line === 1 || anywhere)\n      ) {\n        buffer = fence(matter, 'open')\n        bufferIndex = 0\n\n        if (code === buffer.charCodeAt(bufferIndex)) {\n          effects.enter(frontmatterType)\n          effects.enter(fenceType)\n          effects.enter(sequenceType)\n          return openSequence(code)\n        }\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * In open sequence.\n     *\n     * ```markdown\n     * > | ---\n     *     ^\n     *   | title: \"Venus\"\n     *   | ---\n     * ```\n     *\n     * @type {State}\n     */\n    function openSequence(code) {\n      if (bufferIndex === buffer.length) {\n        effects.exit(sequenceType)\n\n        if (markdownSpace(code)) {\n          effects.enter(types.whitespace)\n          return openSequenceWhitespace(code)\n        }\n\n        return openAfter(code)\n      }\n\n      if (code === buffer.charCodeAt(bufferIndex++)) {\n        effects.consume(code)\n        return openSequence\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * In whitespace after open sequence.\n     *\n     * ```markdown\n     * > | ---␠\n     *        ^\n     *   | title: \"Venus\"\n     *   | ---\n     * ```\n     *\n     * @type {State}\n     */\n    function openSequenceWhitespace(code) {\n      if (markdownSpace(code)) {\n        effects.consume(code)\n        return openSequenceWhitespace\n      }\n\n      effects.exit(types.whitespace)\n      return openAfter(code)\n    }\n\n    /**\n     * After open sequence.\n     *\n     * ```markdown\n     * > | ---\n     *        ^\n     *   | title: \"Venus\"\n     *   | ---\n     * ```\n     *\n     * @type {State}\n     */\n    function openAfter(code) {\n      if (markdownLineEnding(code)) {\n        effects.exit(fenceType)\n        effects.enter(types.lineEnding)\n        effects.consume(code)\n        effects.exit(types.lineEnding)\n        // Get ready for closing fence.\n        buffer = fence(matter, 'close')\n        bufferIndex = 0\n        return effects.attempt(closingFenceConstruct, after, contentStart)\n      }\n\n      // EOF is not okay.\n      return nok(code)\n    }\n\n    /**\n     * Start of content chunk.\n     *\n     * ```markdown\n     *   | ---\n     * > | title: \"Venus\"\n     *     ^\n     *   | ---\n     * ```\n     *\n     * @type {State}\n     */\n    function contentStart(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        return contentEnd(code)\n      }\n\n      effects.enter(valueType)\n      return contentInside(code)\n    }\n\n    /**\n     * In content chunk.\n     *\n     * ```markdown\n     *   | ---\n     * > | title: \"Venus\"\n     *     ^\n     *   | ---\n     * ```\n     *\n     * @type {State}\n     */\n    function contentInside(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        effects.exit(valueType)\n        return contentEnd(code)\n      }\n\n      effects.consume(code)\n      return contentInside\n    }\n\n    /**\n     * End of content chunk.\n     *\n     * ```markdown\n     *   | ---\n     * > | title: \"Venus\"\n     *                   ^\n     *   | ---\n     * ```\n     *\n     * @type {State}\n     */\n    function contentEnd(code) {\n      // Require a closing fence.\n      if (code === codes.eof) {\n        return nok(code)\n      }\n\n      // Can only be an eol.\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return effects.attempt(closingFenceConstruct, after, contentStart)\n    }\n\n    /**\n     * After frontmatter.\n     *\n     * ```markdown\n     *   | ---\n     *   | title: \"Venus\"\n     * > | ---\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function after(code) {\n      // `code` must be eol/eof.\n      effects.exit(frontmatterType)\n      return ok(code)\n    }\n  }\n\n  /** @type {Tokenizer} */\n  function tokenizeClosingFence(effects, ok, nok) {\n    let bufferIndex = 0\n\n    return closeStart\n\n    /**\n     * Start of close sequence.\n     *\n     * ```markdown\n     *   | ---\n     *   | title: \"Venus\"\n     * > | ---\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function closeStart(code) {\n      if (code === buffer.charCodeAt(bufferIndex)) {\n        effects.enter(fenceType)\n        effects.enter(sequenceType)\n        return closeSequence(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * In close sequence.\n     *\n     * ```markdown\n     *   | ---\n     *   | title: \"Venus\"\n     * > | ---\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function closeSequence(code) {\n      if (bufferIndex === buffer.length) {\n        effects.exit(sequenceType)\n\n        if (markdownSpace(code)) {\n          effects.enter(types.whitespace)\n          return closeSequenceWhitespace(code)\n        }\n\n        return closeAfter(code)\n      }\n\n      if (code === buffer.charCodeAt(bufferIndex++)) {\n        effects.consume(code)\n        return closeSequence\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * In whitespace after close sequence.\n     *\n     * ```markdown\n     * > | ---\n     *   | title: \"Venus\"\n     *   | ---␠\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function closeSequenceWhitespace(code) {\n      if (markdownSpace(code)) {\n        effects.consume(code)\n        return closeSequenceWhitespace\n      }\n\n      effects.exit(types.whitespace)\n      return closeAfter(code)\n    }\n\n    /**\n     * After close sequence.\n     *\n     * ```markdown\n     *   | ---\n     *   | title: \"Venus\"\n     * > | ---\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function closeAfter(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        effects.exit(fenceType)\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @param {Matter} matter\n * @param {'close' | 'open'} prop\n * @returns {string}\n */\nfunction fence(matter, prop) {\n  return matter.marker\n    ? pick(matter.marker, prop).repeat(3)\n    : // @ts-expect-error: They’re mutually exclusive.\n      pick(matter.fence, prop)\n}\n\n/**\n * @param {Info | string} schema\n * @param {'close' | 'open'} prop\n * @returns {string}\n */\nfunction pick(schema, prop) {\n  return typeof schema === 'string' ? schema : schema[prop]\n}\n", "export default function escapeStringRegexp(string) {\n\tif (typeof string !== 'string') {\n\t\tthrow new TypeError('Expected a string');\n\t}\n\n\t// Escape characters with special meaning either inside or outside character sets.\n\t// Use a simple backslash escape when it’s always valid, and a `\\xnn` escape when the simpler form would be disallowed by Unicode patterns’ stricter grammar.\n\treturn string\n\t\t.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&')\n\t\t.replace(/-/g, '\\\\x2d');\n}\n", "/**\n * @typedef {import('mdast').Literal} Literal\n *\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n *\n * @typedef {import('micromark-extension-frontmatter').Info} Info\n * @typedef {import('micromark-extension-frontmatter').Matter} Matter\n * @typedef {import('micromark-extension-frontmatter').Options} Options\n */\n\nimport {ok as assert} from 'devlop'\nimport {toMatters} from 'micromark-extension-frontmatter'\nimport escapeStringRegexp from 'escape-string-regexp'\n\n/**\n * Create an extension for `mdast-util-from-markdown`.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown`.\n */\nexport function frontmatterFromMarkdown(options) {\n  const matters = toMatters(options)\n  /** @type {FromMarkdownExtension['enter']} */\n  const enter = {}\n  /** @type {FromMarkdownExtension['exit']} */\n  const exit = {}\n  let index = -1\n\n  while (++index < matters.length) {\n    const matter = matters[index]\n    enter[matter.type] = opener(matter)\n    exit[matter.type] = close\n    exit[matter.type + 'Value'] = value\n  }\n\n  return {enter, exit}\n}\n\n/**\n * @param {Matter} matter\n * @returns {FromMarkdownHandle} enter\n */\nfunction opener(matter) {\n  return open\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function open(token) {\n    // @ts-expect-error: custom.\n    this.enter({type: matter.type, value: ''}, token)\n    this.buffer()\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction close(token) {\n  const data = this.resume()\n  const node = this.stack[this.stack.length - 1]\n  assert('value' in node)\n  this.exit(token)\n  // Remove the initial and final eol.\n  node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '')\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction value(token) {\n  this.config.enter.data.call(this, token)\n  this.config.exit.data.call(this, token)\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown`.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown`.\n */\nexport function frontmatterToMarkdown(options) {\n  /** @type {ToMarkdownExtension['unsafe']} */\n  const unsafe = []\n  /** @type {ToMarkdownExtension['handlers']} */\n  const handlers = {}\n  const matters = toMatters(options)\n  let index = -1\n\n  while (++index < matters.length) {\n    const matter = matters[index]\n\n    // @ts-expect-error: this can add custom frontmatter nodes.\n    // Typing those is the responsibility of the end user.\n    handlers[matter.type] = handler(matter)\n\n    const open = fence(matter, 'open')\n\n    unsafe.push({\n      atBreak: true,\n      character: open.charAt(0),\n      after: escapeStringRegexp(open.charAt(1))\n    })\n  }\n\n  return {unsafe, handlers}\n}\n\n/**\n * Create a handle that can serialize a frontmatter node as markdown.\n *\n * @param {Matter} matter\n *   Structure.\n * @returns {(node: Literal) => string} enter\n *   Handler.\n */\nfunction handler(matter) {\n  const open = fence(matter, 'open')\n  const close = fence(matter, 'close')\n\n  return handle\n\n  /**\n   * Serialize a frontmatter node as markdown.\n   *\n   * @param {Literal} node\n   *   Node to serialize.\n   * @returns {string}\n   *   Serialized node.\n   */\n  function handle(node) {\n    return open + (node.value ? '\\n' + node.value : '') + '\\n' + close\n  }\n}\n\n/**\n * Get an `open` or `close` fence.\n *\n * @param {Matter} matter\n *   Structure.\n * @param {'close' | 'open'} prop\n *   Field to get.\n * @returns {string}\n *   Fence.\n */\nfunction fence(matter, prop) {\n  return matter.marker\n    ? pick(matter.marker, prop).repeat(3)\n    : // @ts-expect-error: They’re mutually exclusive.\n      pick(matter.fence, prop)\n}\n\n/**\n * Take `open` or `close` fields when schema is an info object, or use the\n * given value when it is a string.\n *\n * @param {Info | string} schema\n *   Info object or value.\n * @param {'close' | 'open'} prop\n *   Field to get.\n * @returns {string}\n *   Thing to use for the opening or closing.\n */\nfunction pick(schema, prop) {\n  return typeof schema === 'string' ? schema : schema[prop]\n}\n", "/// <reference types=\"remark-parse\" />\n/// <reference types=\"remark-stringify\" />\n\n/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('micromark-extension-frontmatter').Options} Options\n * @typedef {import('unified').Processor<Root>} Processor\n */\n\nimport {\n  frontmatterFromMarkdown,\n  frontmatterToMarkdown\n} from 'mdast-util-frontmatter'\nimport {frontmatter} from 'micromark-extension-frontmatter'\n\n/** @type {Options} */\nconst emptyOptions = 'yaml'\n\n/**\n * Add support for frontmatter.\n *\n * ###### Notes\n *\n * Doesn’t parse the data inside them: create your own plugin to do that.\n *\n * @param {Options | null | undefined} [options='yaml']\n *   Configuration (default: `'yaml'`).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function remarkFrontmatter(options) {\n  // @ts-expect-error: TS is wrong about `this`.\n  // eslint-disable-next-line unicorn/no-this-assignment\n  const self = /** @type {Processor} */ (this)\n  const settings = options || emptyOptions\n  const data = self.data()\n\n  const micromarkExtensions =\n    data.micromarkExtensions || (data.micromarkExtensions = [])\n  const fromMarkdownExtensions =\n    data.fromMarkdownExtensions || (data.fromMarkdownExtensions = [])\n  const toMarkdownExtensions =\n    data.toMarkdownExtensions || (data.toMarkdownExtensions = [])\n\n  micromarkExtensions.push(frontmatter(settings))\n  fromMarkdownExtensions.push(frontmatterFromMarkdown(settings))\n  toMarkdownExtensions.push(frontmatterToMarkdown(settings))\n}\n"], "mappings": ";;;;;;;;;;;;;;;AACA,oBAAsB;AAEf,IAAM,QAAQ,OAAO,OAAO,OAAO,KAAK,GAAG;AAAA,EAChD,MAAM,OAAO,SAAS;AAAA,EACtB,OAAO,OAAO,UAAU;AAAA,EACxB,WAAW,OAAO,cAAc;AAAA,EAChC,QAAQ,OAAO,WAAW;AAAA,EAC1B,MAAM,OAAO,SAAS;AAAA,EACtB,KAAK,OAAO,QAAQ;AACtB,CAAC;AASM,SAAS,OAAO,aAAa;AAGlC,iBAAe,cAAc,YAAY,eAAe,YAAY;AAEpE,SAAO;AAWP,WAAS,eAAe,WAAW,QAAQ;AAEzC,UAAM,SAAS,aAAS,cAAAA,SAAU,QAAQ,GAAG,MAAM,IAAI;AACvD,WAAO,IAAI,YAAY,MAAM;AAAA,EAC/B;AACF;;;ACyBA,IAAM,MAAM,CAAC,EAAE;AACf,IAAM,UAAU,EAAC,MAAM,KAAK,MAAM,IAAG;AAU9B,SAAS,UAAU,SAAS;AAEjC,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAGZ,QAAM,mBAAmB,MAAM,QAAQ,OAAO,IAC1C,UACA,UACA,CAAC,OAAO,IACR,CAAC,MAAM;AAEX,SAAO,EAAE,QAAQ,iBAAiB,QAAQ;AACxC,WAAO,KAAK,IAAI,OAAO,iBAAiB,KAAK,CAAC;AAAA,EAChD;AAEA,SAAO;AACT;AAUA,SAAS,OAAO,QAAQ;AACtB,MAAI,SAAS;AAEb,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,CAAC,IAAI,KAAK,SAAS,MAAM,GAAG;AAC9B,YAAM,MAAM,sCAAsC,MAAM;AAAA,IAC1D;AAEA,aAAS,EAAC,MAAM,QAAQ,QAAQ,QAAQ,MAAM,EAAC;AAAA,EACjD,WAAW,OAAO,WAAW,UAAU;AACrC,UAAM,MAAM,6CAA6C,MAAM;AAAA,EACjE;AAEA,MAAI,CAAC,IAAI,KAAK,QAAQ,MAAM,GAAG;AAC7B,UAAM,MAAM,iCAAiC,MAAM;AAAA,EACrD;AAEA,MAAI,CAAC,IAAI,KAAK,QAAQ,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,QAAQ,GAAG;AAC7D,UAAM,MAAM,8CAA8C,MAAM;AAAA,EAClE;AAEA,SAAO;AACT;;;AClGO,SAAS,YAAY,SAAS;AACnC,QAAM,UAAU,UAAU,OAAO;AAEjC,QAAM,OAAO,CAAC;AACd,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,QAAQ,QAAQ;AAC/B,UAAMC,UAAS,QAAQ,KAAK;AAC5B,UAAM,OAAO,MAAMA,SAAQ,MAAM,EAAE,WAAW,CAAC;AAC/C,UAAM,YAAY,gBAAgBA,OAAM;AACxC,UAAM,WAAW,KAAK,IAAI;AAE1B,QAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,eAAS,KAAK,SAAS;AAAA,IACzB,OAAO;AAEL,WAAK,IAAI,IAAI,CAAC,SAAS;AAAA,IACzB;AAAA,EACF;AAEA,SAAO,EAAC,KAAI;AACd;AAMA,SAAS,gBAAgBA,SAAQ;AAC/B,QAAM,WAAWA,QAAO;AACxB,QAAM;AAAA;AAAA,IAA4CA,QAAO;AAAA;AACzD,QAAM;AAAA;AAAA,IAAsC,kBAAkB;AAAA;AAC9D,QAAM;AAAA;AAAA,IAAyC,YAAY;AAAA;AAC3D,QAAM;AAAA;AAAA,IAAsC,kBAAkB;AAAA;AAC9D,QAAM,wBAAwB,EAAC,UAAU,sBAAsB,SAAS,KAAI;AAO5E,MAAI;AACJ,MAAI,cAAc;AAElB,SAAO,EAAC,UAAU,qBAAqB,UAAU,KAAI;AAMrD,WAAS,oBAAoB,SAASC,KAAI,KAAK;AAC7C,UAAM,OAAO;AAEb,WAAO;AAcP,aAAS,MAAM,MAAM;AACnB,YAAM,WAAW,KAAK,IAAI;AAE1B;AAAA;AAAA,QAEE,SAAS,WAAW;AAAA,SAEnB,SAAS,SAAS,KAAK;AAAA,QACxB;AACA,iBAAS,MAAMD,SAAQ,MAAM;AAC7B,sBAAc;AAEd,YAAI,SAAS,OAAO,WAAW,WAAW,GAAG;AAC3C,kBAAQ,MAAM,eAAe;AAC7B,kBAAQ,MAAM,SAAS;AACvB,kBAAQ,MAAM,YAAY;AAC1B,iBAAO,aAAa,IAAI;AAAA,QAC1B;AAAA,MACF;AAEA,aAAO,IAAI,IAAI;AAAA,IACjB;AAcA,aAAS,aAAa,MAAM;AAC1B,UAAI,gBAAgB,OAAO,QAAQ;AACjC,gBAAQ,KAAK,YAAY;AAEzB,YAAI,cAAc,IAAI,GAAG;AACvB,kBAAQ,MAAM,MAAM,UAAU;AAC9B,iBAAO,uBAAuB,IAAI;AAAA,QACpC;AAEA,eAAO,UAAU,IAAI;AAAA,MACvB;AAEA,UAAI,SAAS,OAAO,WAAW,aAAa,GAAG;AAC7C,gBAAQ,QAAQ,IAAI;AACpB,eAAO;AAAA,MACT;AAEA,aAAO,IAAI,IAAI;AAAA,IACjB;AAcA,aAAS,uBAAuB,MAAM;AACpC,UAAI,cAAc,IAAI,GAAG;AACvB,gBAAQ,QAAQ,IAAI;AACpB,eAAO;AAAA,MACT;AAEA,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO,UAAU,IAAI;AAAA,IACvB;AAcA,aAAS,UAAU,MAAM;AACvB,UAAI,mBAAmB,IAAI,GAAG;AAC5B,gBAAQ,KAAK,SAAS;AACtB,gBAAQ,MAAM,MAAM,UAAU;AAC9B,gBAAQ,QAAQ,IAAI;AACpB,gBAAQ,KAAK,MAAM,UAAU;AAE7B,iBAAS,MAAMA,SAAQ,OAAO;AAC9B,sBAAc;AACd,eAAO,QAAQ,QAAQ,uBAAuB,OAAO,YAAY;AAAA,MACnE;AAGA,aAAO,IAAI,IAAI;AAAA,IACjB;AAcA,aAAS,aAAa,MAAM;AAC1B,UAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,eAAO,WAAW,IAAI;AAAA,MACxB;AAEA,cAAQ,MAAM,SAAS;AACvB,aAAO,cAAc,IAAI;AAAA,IAC3B;AAcA,aAAS,cAAc,MAAM;AAC3B,UAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,gBAAQ,KAAK,SAAS;AACtB,eAAO,WAAW,IAAI;AAAA,MACxB;AAEA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAcA,aAAS,WAAW,MAAM;AAExB,UAAI,SAAS,MAAM,KAAK;AACtB,eAAO,IAAI,IAAI;AAAA,MACjB;AAGA,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO,QAAQ,QAAQ,uBAAuB,OAAO,YAAY;AAAA,IACnE;AAcA,aAAS,MAAM,MAAM;AAEnB,cAAQ,KAAK,eAAe;AAC5B,aAAOC,IAAG,IAAI;AAAA,IAChB;AAAA,EACF;AAGA,WAAS,qBAAqB,SAASA,KAAI,KAAK;AAC9C,QAAIC,eAAc;AAElB,WAAO;AAcP,aAAS,WAAW,MAAM;AACxB,UAAI,SAAS,OAAO,WAAWA,YAAW,GAAG;AAC3C,gBAAQ,MAAM,SAAS;AACvB,gBAAQ,MAAM,YAAY;AAC1B,eAAO,cAAc,IAAI;AAAA,MAC3B;AAEA,aAAO,IAAI,IAAI;AAAA,IACjB;AAcA,aAAS,cAAc,MAAM;AAC3B,UAAIA,iBAAgB,OAAO,QAAQ;AACjC,gBAAQ,KAAK,YAAY;AAEzB,YAAI,cAAc,IAAI,GAAG;AACvB,kBAAQ,MAAM,MAAM,UAAU;AAC9B,iBAAO,wBAAwB,IAAI;AAAA,QACrC;AAEA,eAAO,WAAW,IAAI;AAAA,MACxB;AAEA,UAAI,SAAS,OAAO,WAAWA,cAAa,GAAG;AAC7C,gBAAQ,QAAQ,IAAI;AACpB,eAAO;AAAA,MACT;AAEA,aAAO,IAAI,IAAI;AAAA,IACjB;AAcA,aAAS,wBAAwB,MAAM;AACrC,UAAI,cAAc,IAAI,GAAG;AACvB,gBAAQ,QAAQ,IAAI;AACpB,eAAO;AAAA,MACT;AAEA,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO,WAAW,IAAI;AAAA,IACxB;AAcA,aAAS,WAAW,MAAM;AACxB,UAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,gBAAQ,KAAK,SAAS;AACtB,eAAOD,IAAG,IAAI;AAAA,MAChB;AAEA,aAAO,IAAI,IAAI;AAAA,IACjB;AAAA,EACF;AACF;AAOA,SAAS,MAAMD,SAAQ,MAAM;AAC3B,SAAOA,QAAO,SACV,KAAKA,QAAO,QAAQ,IAAI,EAAE,OAAO,CAAC;AAAA;AAAA,IAElC,KAAKA,QAAO,OAAO,IAAI;AAAA;AAC7B;AAOA,SAAS,KAAK,QAAQ,MAAM;AAC1B,SAAO,OAAO,WAAW,WAAW,SAAS,OAAO,IAAI;AAC1D;;;ACzZe,SAAR,mBAAoC,QAAQ;AAClD,MAAI,OAAO,WAAW,UAAU;AAC/B,UAAM,IAAI,UAAU,mBAAmB;AAAA,EACxC;AAIA,SAAO,OACL,QAAQ,uBAAuB,MAAM,EACrC,QAAQ,MAAM,OAAO;AACxB;;;ACeO,SAAS,wBAAwB,SAAS;AAC/C,QAAM,UAAU,UAAU,OAAO;AAEjC,QAAM,QAAQ,CAAC;AAEf,QAAM,OAAO,CAAC;AACd,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,QAAQ,QAAQ;AAC/B,UAAMG,UAAS,QAAQ,KAAK;AAC5B,UAAMA,QAAO,IAAI,IAAI,OAAOA,OAAM;AAClC,SAAKA,QAAO,IAAI,IAAI;AACpB,SAAKA,QAAO,OAAO,OAAO,IAAI;AAAA,EAChC;AAEA,SAAO,EAAC,OAAO,KAAI;AACrB;AAMA,SAAS,OAAOA,SAAQ;AACtB,SAAO;AAMP,WAAS,KAAK,OAAO;AAEnB,SAAK,MAAM,EAAC,MAAMA,QAAO,MAAM,OAAO,GAAE,GAAG,KAAK;AAChD,SAAK,OAAO;AAAA,EACd;AACF;AAMA,SAAS,MAAM,OAAO;AACpB,QAAM,OAAO,KAAK,OAAO;AACzB,QAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,KAAO,WAAW,IAAI;AACtB,OAAK,KAAK,KAAK;AAEf,OAAK,QAAQ,KAAK,QAAQ,4BAA4B,EAAE;AAC1D;AAMA,SAAS,MAAM,OAAO;AACpB,OAAK,OAAO,MAAM,KAAK,KAAK,MAAM,KAAK;AACvC,OAAK,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK;AACxC;AAUO,SAAS,sBAAsB,SAAS;AAE7C,QAAM,SAAS,CAAC;AAEhB,QAAM,WAAW,CAAC;AAClB,QAAM,UAAU,UAAU,OAAO;AACjC,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,QAAQ,QAAQ;AAC/B,UAAMA,UAAS,QAAQ,KAAK;AAI5B,aAASA,QAAO,IAAI,IAAI,QAAQA,OAAM;AAEtC,UAAM,OAAOC,OAAMD,SAAQ,MAAM;AAEjC,WAAO,KAAK;AAAA,MACV,SAAS;AAAA,MACT,WAAW,KAAK,OAAO,CAAC;AAAA,MACxB,OAAO,mBAAmB,KAAK,OAAO,CAAC,CAAC;AAAA,IAC1C,CAAC;AAAA,EACH;AAEA,SAAO,EAAC,QAAQ,SAAQ;AAC1B;AAUA,SAAS,QAAQA,SAAQ;AACvB,QAAM,OAAOC,OAAMD,SAAQ,MAAM;AACjC,QAAME,SAAQD,OAAMD,SAAQ,OAAO;AAEnC,SAAO;AAUP,WAAS,OAAO,MAAM;AACpB,WAAO,QAAQ,KAAK,QAAQ,OAAO,KAAK,QAAQ,MAAM,OAAOE;AAAA,EAC/D;AACF;AAYA,SAASD,OAAMD,SAAQ,MAAM;AAC3B,SAAOA,QAAO,SACVG,MAAKH,QAAO,QAAQ,IAAI,EAAE,OAAO,CAAC;AAAA;AAAA,IAElCG,MAAKH,QAAO,OAAO,IAAI;AAAA;AAC7B;AAaA,SAASG,MAAK,QAAQ,MAAM;AAC1B,SAAO,OAAO,WAAW,WAAW,SAAS,OAAO,IAAI;AAC1D;;;AC/JA,IAAM,eAAe;AAcN,SAAR,kBAAmC,SAAS;AAGjD,QAAM;AAAA;AAAA,IAAiC;AAAA;AACvC,QAAM,WAAW,WAAW;AAC5B,QAAM,OAAO,KAAK,KAAK;AAEvB,QAAM,sBACJ,KAAK,wBAAwB,KAAK,sBAAsB,CAAC;AAC3D,QAAM,yBACJ,KAAK,2BAA2B,KAAK,yBAAyB,CAAC;AACjE,QAAM,uBACJ,KAAK,yBAAyB,KAAK,uBAAuB,CAAC;AAE7D,sBAAoB,KAAK,YAAY,QAAQ,CAAC;AAC9C,yBAAuB,KAAK,wBAAwB,QAAQ,CAAC;AAC7D,uBAAqB,KAAK,sBAAsB,QAAQ,CAAC;AAC3D;", "names": ["formatter", "matter", "ok", "bufferIndex", "matter", "fence", "close", "pick"]}