{"version": 3, "sources": ["../../refractor/lang/ftl.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = ftl\nftl.displayName = 'ftl'\nftl.aliases = []\nfunction ftl(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    // https://freemarker.apache.org/docs/dgui_template_exp.html\n    // FTL expression with 4 levels of nesting supported\n    var FTL_EXPR =\n      /[^<()\"']|\\((?:<expr>)*\\)|<(?!#--)|<#--(?:[^-]|-(?!->))*-->|\"(?:[^\\\\\"]|\\\\.)*\"|'(?:[^\\\\']|\\\\.)*'/\n        .source\n    for (var i = 0; i < 2; i++) {\n      FTL_EXPR = FTL_EXPR.replace(/<expr>/g, function () {\n        return FTL_EXPR\n      })\n    }\n    FTL_EXPR = FTL_EXPR.replace(/<expr>/g, /[^\\s\\S]/.source)\n    var ftl = {\n      comment: /<#--[\\s\\S]*?-->/,\n      string: [\n        {\n          // raw string\n          pattern: /\\br(\"|')(?:(?!\\1)[^\\\\]|\\\\.)*\\1/,\n          greedy: true\n        },\n        {\n          pattern: RegExp(\n            /(\"|')(?:(?!\\1|\\$\\{)[^\\\\]|\\\\.|\\$\\{(?:(?!\\})(?:<expr>))*\\})*\\1/.source.replace(\n              /<expr>/g,\n              function () {\n                return FTL_EXPR\n              }\n            )\n          ),\n          greedy: true,\n          inside: {\n            interpolation: {\n              pattern: RegExp(\n                /((?:^|[^\\\\])(?:\\\\\\\\)*)\\$\\{(?:(?!\\})(?:<expr>))*\\}/.source.replace(\n                  /<expr>/g,\n                  function () {\n                    return FTL_EXPR\n                  }\n                )\n              ),\n              lookbehind: true,\n              inside: {\n                'interpolation-punctuation': {\n                  pattern: /^\\$\\{|\\}$/,\n                  alias: 'punctuation'\n                },\n                rest: null\n              }\n            }\n          }\n        }\n      ],\n      keyword: /\\b(?:as)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      'builtin-function': {\n        pattern: /((?:^|[^?])\\?\\s*)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      function: /\\b\\w+(?=\\s*\\()/,\n      number: /\\b\\d+(?:\\.\\d+)?\\b/,\n      operator:\n        /\\.\\.[<*!]?|->|--|\\+\\+|&&|\\|\\||\\?{1,2}|[-+*/%!=<>]=?|\\b(?:gt|gte|lt|lte)\\b/,\n      punctuation: /[,;.:()[\\]{}]/\n    }\n    ftl.string[1].inside.interpolation.inside.rest = ftl\n    Prism.languages.ftl = {\n      'ftl-comment': {\n        // the pattern is shortened to be more efficient\n        pattern: /^<#--[\\s\\S]*/,\n        alias: 'comment'\n      },\n      'ftl-directive': {\n        pattern: /^<[\\s\\S]+>$/,\n        inside: {\n          directive: {\n            pattern: /(^<\\/?)[#@][a-z]\\w*/i,\n            lookbehind: true,\n            alias: 'keyword'\n          },\n          punctuation: /^<\\/?|\\/?>$/,\n          content: {\n            pattern: /\\s*\\S[\\s\\S]*/,\n            alias: 'ftl',\n            inside: ftl\n          }\n        }\n      },\n      'ftl-interpolation': {\n        pattern: /^\\$\\{[\\s\\S]*\\}$/,\n        inside: {\n          punctuation: /^\\$\\{|\\}$/,\n          content: {\n            pattern: /\\s*\\S[\\s\\S]*/,\n            alias: 'ftl',\n            inside: ftl\n          }\n        }\n      }\n    }\n    Prism.hooks.add('before-tokenize', function (env) {\n      // eslint-disable-next-line regexp/no-useless-lazy\n      var pattern = RegExp(\n        /<#--[\\s\\S]*?-->|<\\/?[#@][a-zA-Z](?:<expr>)*?>|\\$\\{(?:<expr>)*?\\}/.source.replace(\n          /<expr>/g,\n          function () {\n            return FTL_EXPR\n          }\n        ),\n        'gi'\n      )\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'ftl',\n        pattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'ftl')\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,SAAS,yBAAyB;AACvC,OAAC,SAAUA,QAAO;AAGjB,YAAI,WACF,iGACG;AACL,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,qBAAW,SAAS,QAAQ,WAAW,WAAY;AACjD,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,mBAAW,SAAS,QAAQ,WAAW,UAAU,MAAM;AACvD,YAAIC,OAAM;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,YACN;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP,+DAA+D,OAAO;AAAA,kBACpE;AAAA,kBACA,WAAY;AACV,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF;AAAA,cACA,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,eAAe;AAAA,kBACb,SAAS;AAAA,oBACP,oDAAoD,OAAO;AAAA,sBACzD;AAAA,sBACA,WAAY;AACV,+BAAO;AAAA,sBACT;AAAA,oBACF;AAAA,kBACF;AAAA,kBACA,YAAY;AAAA,kBACZ,QAAQ;AAAA,oBACN,6BAA6B;AAAA,sBAC3B,SAAS;AAAA,sBACT,OAAO;AAAA,oBACT;AAAA,oBACA,MAAM;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,SAAS;AAAA,UACT,SAAS;AAAA,UACT,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,UACE;AAAA,UACF,aAAa;AAAA,QACf;AACA,QAAAA,KAAI,OAAO,CAAC,EAAE,OAAO,cAAc,OAAO,OAAOA;AACjD,QAAAD,OAAM,UAAU,MAAM;AAAA,UACpB,eAAe;AAAA;AAAA,YAEb,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,iBAAiB;AAAA,YACf,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,cACA,aAAa;AAAA,cACb,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQC;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,UACA,qBAAqB;AAAA,YACnB,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,cACb,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQA;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,QAAAD,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAEhD,cAAI,UAAU;AAAA,YACZ,mEAAmE,OAAO;AAAA,cACxE;AAAA,cACA,WAAY;AACV,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,YACA;AAAA,UACF;AACA,UAAAA,OAAM,UAAU,mBAAmB,EAAE;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,UAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,KAAK;AAAA,QACtE,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism", "ftl"]}