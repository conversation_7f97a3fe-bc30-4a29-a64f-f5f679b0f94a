---

````markdown
# 🏗️ Blueprint — Layout e Organização do Site

---

## 🔥 Hierarquia Visual do Site

```plaintext
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🧠 Navbar (fixo no topo)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
| 🏠 Home | 📝 Blog | 🚀 Projects | 📜 Devlog | 👾 About | 📬 Contact |
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🖥️ Hero Section (Home)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
[LOGO ou TÍTULO: BLUEPRINT]
[Slogan: "O mapa. A planta. O rascunho da vida dev."]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📑 Seções da Home
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
→ 📝 Últimos Artigos (Blog Preview)
→ 🚀 Projetos em Destaque (Cards)
→ 📜 Últimos Logs do Devlog
→ 👾 Sobre Mim (Mini resumo com botão "Read More")
→ 📬 Contato / Call to Action

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🦾 Footer
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
→ Redes Sociais | GitHub | LinkedIn | Medium | X/Twitter
→ Créditos | © Ano Atual | Powered by Blueprint
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
````

---

## 📝 Página Blog

```plaintext
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📝 Blog — All Posts
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
[ Search Box (opcional) ]

→ Lista de PostCards ↓
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
[📅 Data] [📄 Título do Post]
[Resumo / Excerpt]
[🔗 Read More]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

→ Paginação no final (se quiser)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

## 📄 Página Post (Leitura)

```plaintext
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
[ Título do Post ]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
[📅 Data] [🧠 Categoria] [🕓 Tempo de Leitura]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

→ [Markdown Renderizado do Post]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

→ Footer do Post
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
• Tags  
• Link para próximo e anterior post (opcional)  
• Comentários (Giscus, Utterances ou nada)  
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

## 🚀 Página Projects

```plaintext
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🚀 Meus Projetos
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
→ Lista de ProjectCards ↓
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
[🚀 Nome do Projeto]
[🔍 Descrição curta]
[⚙️ Tech Stack]
[🔗 Demo] [💻 GitHub] [📄 Docs/Devlog]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

→ Filtros opcionais por status (Ativo | Em Pausa | Arquivado)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

## 📜 Página Devlog

```plaintext
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📜 Devlog — Documentando tudo
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

→ Timeline Vertical ↓
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
[📅 Data]
[🚀 Projeto relacionado]
[Título do log]
[Resumo curto]
[🔗 Read More → Página do log completo (opcional)]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

→ Logs agrupados por mês ou projeto
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

## 👾 Página About

```plaintext
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
👾 About — Quem Sou Eu
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

→ Foto ou Avatar
→ Bio rápida
→ O que é o Blueprint (manifesto pessoal)
→ Stack, tecnologias, skills
→ Links para redes, GitHub, Medium
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

## 📬 Página Contact

```plaintext
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📬 Contact — Bora trocar ideia
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

→ Texto simples: "Me encontra nas redes, bora construir, conversar ou hackear o sistema."
→ Botões/Links para:
   - GitHub
   - LinkedIn
   - Medium
   - X/Twitter
   - E-mail
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

## 🎨 Paleta Visual e Design

| 🎨 Item        | 🔥 Escolha                                                            |
| -------------- | --------------------------------------------------------------------- |
| **Tema**       | Dark Mode default, Light opcional                                     |
| **Cores base** | Preto, Neon Azul, Neon Verde, Branco                                  |
| **Fonte**      | Space Grotesk / JetBrains Mono / Inter                                |
| **Estilo UI**  | Minimalista + Cyberpunk + Blueprint lines                             |
| **Texturas**   | Background preto + linhas blueprint + glitch/matrix code nos detalhes |
| **Animações**  | Fade, hover glow, glitch leve, smooth slide                           |

---

## 🏴‍☠️ Elementos Visuais Clássicos do Blueprint

* ✔️ Linhas blueprint desenhadas no fundo
* ✔️ Códigos matrix caindo nas laterais (leve, não poluído)
* ✔️ Glitch sutil nos headings
* ✔️ Neon em detalhes de hover e bordas
* ✔️ Shadow glow nos cards e botões
* ✔️ Divisores → linhas finas, pontilhadas ou tracejadas (estilo blueprint clássico)

---

## 🔥 Call Final

> **Blueprint — O mapa. A planta. O rascunho da vida dev.
> Simples, direto, hacker, poético, funcional e brutal.**

---

```

---

