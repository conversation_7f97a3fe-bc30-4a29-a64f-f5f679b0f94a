{"version": 3, "sources": ["../../refractor/lang/less.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = less\nless.displayName = 'less'\nless.aliases = []\nfunction less(Prism) {\n  /* FIXME :\n:extend() is not handled specifically : its highlighting is buggy.\nMixin usage must be inside a ruleset to be highlighted.\nAt-rules (e.g. import) containing interpolations are buggy.\nDetached rulesets are highlighted as at-rules.\nA comment before a mixin usage prevents the latter to be properly highlighted.\n*/\n  Prism.languages.less = Prism.languages.extend('css', {\n    comment: [\n      /\\/\\*[\\s\\S]*?\\*\\//,\n      {\n        pattern: /(^|[^\\\\])\\/\\/.*/,\n        lookbehind: true\n      }\n    ],\n    atrule: {\n      pattern:\n        /@[\\w-](?:\\((?:[^(){}]|\\([^(){}]*\\))*\\)|[^(){};\\s]|\\s+(?!\\s))*?(?=\\s*\\{)/,\n      inside: {\n        punctuation: /[:()]/\n      }\n    },\n    // selectors and mixins are considered the same\n    selector: {\n      pattern:\n        /(?:@\\{[\\w-]+\\}|[^{};\\s@])(?:@\\{[\\w-]+\\}|\\((?:[^(){}]|\\([^(){}]*\\))*\\)|[^(){};@\\s]|\\s+(?!\\s))*?(?=\\s*\\{)/,\n      inside: {\n        // mixin parameters\n        variable: /@+[\\w-]+/\n      }\n    },\n    property: /(?:@\\{[\\w-]+\\}|[\\w-])+(?:\\+_?)?(?=\\s*:)/,\n    operator: /[+\\-*\\/]/\n  })\n  Prism.languages.insertBefore('less', 'property', {\n    variable: [\n      // Variable declaration (the colon must be consumed!)\n      {\n        pattern: /@[\\w-]+\\s*:/,\n        inside: {\n          punctuation: /:/\n        }\n      }, // Variable usage\n      /@@?[\\w-]+/\n    ],\n    'mixin-usage': {\n      pattern: /([{;]\\s*)[.#](?!\\d)[\\w-].*?(?=[(;])/,\n      lookbehind: true,\n      alias: 'function'\n    }\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AAQnB,YAAM,UAAU,OAAO,MAAM,UAAU,OAAO,OAAO;AAAA,QACnD,SAAS;AAAA,UACP;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,SACE;AAAA,UACF,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA;AAAA,QAEA,UAAU;AAAA,UACR,SACE;AAAA,UACF,QAAQ;AAAA;AAAA,YAEN,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,CAAC;AACD,YAAM,UAAU,aAAa,QAAQ,YAAY;AAAA,QAC/C,UAAU;AAAA;AAAA,UAER;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA;AAAA,UACA;AAAA,QACF;AAAA,QACA,eAAe;AAAA,UACb,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}