import { Code2, Filter, Search } from 'lucide-react';
import { useState } from 'react';
import DevlogCard from '../components/devlog/DevlogCard';
import { useDevlogs } from '../hooks/useContent';

function Devlog() {
  const { devlogs, loading, error } = useDevlogs();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [selectedProject, setSelectedProject] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  // Get unique values for filters
  const allTypes = [...new Set(devlogs.map((d) => d.type))].sort();
  const allProjects = [...new Set(devlogs.map((d) => d.project))].sort();
  const allStatuses = [...new Set(devlogs.map((d) => d.status))].sort();

  // Filter devlogs
  const filteredDevlogs = devlogs.filter((devlog) => {
    const matchesSearch =
      !searchTerm ||
      devlog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      devlog.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      devlog.project.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = !selectedType || devlog.type === selectedType;
    const matchesProject =
      !selectedProject || devlog.project === selectedProject;
    const matchesStatus = !selectedStatus || devlog.status === selectedStatus;

    return matchesSearch && matchesType && matchesProject && matchesStatus;
  });

  const getTypeLabel = (type) => {
    switch (type) {
      case 'milestone':
        return 'Marco';
      case 'feature':
        return 'Feature';
      case 'bugfix':
        return 'Bugfix';
      case 'update':
        return 'Update';
      default:
        return type;
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'completed':
        return 'Concluído';
      case 'in-progress':
        return 'Em progresso';
      case 'paused':
        return 'Pausado';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="pt-16 min-h-screen">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="text-cyber-purple font-mono">
              {'>'} Carregando timeline...
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pt-16 min-h-screen">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="card text-center">
            <h2 className="text-2xl font-semibold text-red-500 mb-4">
              Erro ao carregar devlogs
            </h2>
            <p className="text-dark-text-secondary">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-16 min-h-screen">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Code2 className="h-12 w-12 text-cyber-purple mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gradient">
              Devlog
            </h1>
          </div>
          <p className="text-xl text-dark-text-secondary">
            Timeline público do desenvolvimento dos projetos
          </p>
          <p className="text-dark-text-secondary mt-2">
            {devlogs.length} {devlogs.length === 1 ? 'entrada' : 'entradas'} no
            devlog
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-dark-text-secondary" />
            <input
              type="text"
              placeholder="Buscar no devlog..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-dark-card border border-dark-border rounded-lg text-dark-text placeholder-dark-text-secondary focus:border-cyber-purple focus:outline-none"
            />
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            {/* Type Filter */}
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="bg-dark-card border border-dark-border rounded-lg px-3 py-2 text-dark-text focus:border-cyber-purple focus:outline-none">
              <option value="">Todos os tipos</option>
              {allTypes.map((type) => (
                <option key={type} value={type}>
                  {getTypeLabel(type)}
                </option>
              ))}
            </select>

            {/* Project Filter */}
            <select
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
              className="bg-dark-card border border-dark-border rounded-lg px-3 py-2 text-dark-text focus:border-cyber-purple focus:outline-none">
              <option value="">Todos os projetos</option>
              {allProjects.map((project) => (
                <option key={project} value={project}>
                  {project}
                </option>
              ))}
            </select>

            {/* Status Filter */}
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="bg-dark-card border border-dark-border rounded-lg px-3 py-2 text-dark-text focus:border-cyber-purple focus:outline-none">
              <option value="">Todos os status</option>
              {allStatuses.map((status) => (
                <option key={status} value={status}>
                  {getStatusLabel(status)}
                </option>
              ))}
            </select>

            {/* Clear Filters */}
            {(searchTerm ||
              selectedType ||
              selectedProject ||
              selectedStatus) && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedType('');
                  setSelectedProject('');
                  setSelectedStatus('');
                }}
                className="px-4 py-2 text-dark-text-secondary hover:text-cyber-purple transition-colors">
                Limpar filtros
              </button>
            )}
          </div>
        </div>

        {/* Timeline */}
        {filteredDevlogs.length > 0 ? (
          <div className="space-y-6">
            {filteredDevlogs.map((devlog, index) => (
              <div key={devlog.id} className="relative">
                {/* Timeline line */}
                {index < filteredDevlogs.length - 1 && (
                  <div className="absolute left-6 top-16 w-0.5 h-full bg-gradient-to-b from-cyber-purple/50 to-transparent"></div>
                )}

                {/* Timeline dot */}
                <div className="absolute left-4 top-8 w-4 h-4 bg-cyber-purple rounded-full border-2 border-dark-bg shadow-lg shadow-cyber-purple/20"></div>

                {/* Content */}
                <div className="ml-12">
                  <DevlogCard devlog={devlog} />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="card text-center">
            <Filter className="h-12 w-12 text-dark-text-secondary mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-gradient mb-4">
              Nenhuma entrada encontrada
            </h2>
            <p className="text-dark-text-secondary">
              {searchTerm || selectedType || selectedProject || selectedStatus
                ? 'Tente ajustar os filtros de busca.'
                : 'Ainda não há entradas no devlog.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default Devlog;
