{"version": 3, "sources": ["../../highlight.js/lib/languages/csharp.js"], "sourcesContent": ["/*\nLanguage: C#\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://docs.microsoft.com/en-us/dotnet/csharp/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction csharp(hljs) {\n  const BUILT_IN_KEYWORDS = [\n    'bool',\n    'byte',\n    'char',\n    'decimal',\n    'delegate',\n    'double',\n    'dynamic',\n    'enum',\n    'float',\n    'int',\n    'long',\n    'nint',\n    'nuint',\n    'object',\n    'sbyte',\n    'short',\n    'string',\n    'ulong',\n    'uint',\n    'ushort'\n  ];\n  const FUNCTION_MODIFIERS = [\n    'public',\n    'private',\n    'protected',\n    'static',\n    'internal',\n    'protected',\n    'abstract',\n    'async',\n    'extern',\n    'override',\n    'unsafe',\n    'virtual',\n    'new',\n    'sealed',\n    'partial'\n  ];\n  const LITERAL_KEYWORDS = [\n    'default',\n    'false',\n    'null',\n    'true'\n  ];\n  const NORMAL_KEYWORDS = [\n    'abstract',\n    'as',\n    'base',\n    'break',\n    'case',\n    'class',\n    'const',\n    'continue',\n    'do',\n    'else',\n    'event',\n    'explicit',\n    'extern',\n    'finally',\n    'fixed',\n    'for',\n    'foreach',\n    'goto',\n    'if',\n    'implicit',\n    'in',\n    'interface',\n    'internal',\n    'is',\n    'lock',\n    'namespace',\n    'new',\n    'operator',\n    'out',\n    'override',\n    'params',\n    'private',\n    'protected',\n    'public',\n    'readonly',\n    'record',\n    'ref',\n    'return',\n    'sealed',\n    'sizeof',\n    'stackalloc',\n    'static',\n    'struct',\n    'switch',\n    'this',\n    'throw',\n    'try',\n    'typeof',\n    'unchecked',\n    'unsafe',\n    'using',\n    'virtual',\n    'void',\n    'volatile',\n    'while'\n  ];\n  const CONTEXTUAL_KEYWORDS = [\n    'add',\n    'alias',\n    'and',\n    'ascending',\n    'async',\n    'await',\n    'by',\n    'descending',\n    'equals',\n    'from',\n    'get',\n    'global',\n    'group',\n    'init',\n    'into',\n    'join',\n    'let',\n    'nameof',\n    'not',\n    'notnull',\n    'on',\n    'or',\n    'orderby',\n    'partial',\n    'remove',\n    'select',\n    'set',\n    'unmanaged',\n    'value|0',\n    'var',\n    'when',\n    'where',\n    'with',\n    'yield'\n  ];\n\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS.concat(CONTEXTUAL_KEYWORDS),\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n  const TITLE_MODE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: '[a-zA-Z](\\\\.?\\\\w)*'\n  });\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      {\n        begin: '\\\\b(0b[01\\']+)'\n      },\n      {\n        begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)(u|U|l|L|ul|UL|f|F|b|B)'\n      },\n      {\n        begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)'\n      }\n    ],\n    relevance: 0\n  };\n  const VERBATIM_STRING = {\n    className: 'string',\n    begin: '@\"',\n    end: '\"',\n    contains: [\n      {\n        begin: '\"\"'\n      }\n    ]\n  };\n  const VERBATIM_STRING_NO_LF = hljs.inherit(VERBATIM_STRING, {\n    illegal: /\\n/\n  });\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const SUBST_NO_LF = hljs.inherit(SUBST, {\n    illegal: /\\n/\n  });\n  const INTERPOLATED_STRING = {\n    className: 'string',\n    begin: /\\$\"/,\n    end: '\"',\n    illegal: /\\n/,\n    contains: [\n      {\n        begin: /\\{\\{/\n      },\n      {\n        begin: /\\}\\}/\n      },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST_NO_LF\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING = {\n    className: 'string',\n    begin: /\\$@\"/,\n    end: '\"',\n    contains: [\n      {\n        begin: /\\{\\{/\n      },\n      {\n        begin: /\\}\\}/\n      },\n      {\n        begin: '\"\"'\n      },\n      SUBST\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING_NO_LF = hljs.inherit(INTERPOLATED_VERBATIM_STRING, {\n    illegal: /\\n/,\n    contains: [\n      {\n        begin: /\\{\\{/\n      },\n      {\n        begin: /\\}\\}/\n      },\n      {\n        begin: '\"\"'\n      },\n      SUBST_NO_LF\n    ]\n  });\n  SUBST.contains = [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.C_BLOCK_COMMENT_MODE\n  ];\n  SUBST_NO_LF.contains = [\n    INTERPOLATED_VERBATIM_STRING_NO_LF,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING_NO_LF,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.inherit(hljs.C_BLOCK_COMMENT_MODE, {\n      illegal: /\\n/\n    })\n  ];\n  const STRING = {\n    variants: [\n      INTERPOLATED_VERBATIM_STRING,\n      INTERPOLATED_STRING,\n      VERBATIM_STRING,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n\n  const GENERIC_MODIFIER = {\n    begin: \"<\",\n    end: \">\",\n    contains: [\n      {\n        beginKeywords: \"in out\"\n      },\n      TITLE_MODE\n    ]\n  };\n  const TYPE_IDENT_RE = hljs.IDENT_RE + '(<' + hljs.IDENT_RE + '(\\\\s*,\\\\s*' + hljs.IDENT_RE + ')*>)?(\\\\[\\\\])?';\n  const AT_IDENTIFIER = {\n    // prevents expressions like `@class` from incorrect flagging\n    // `class` as a keyword\n    begin: \"@\" + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  return {\n    name: 'C#',\n    aliases: [\n      'cs',\n      'c#'\n    ],\n    keywords: KEYWORDS,\n    illegal: /::/,\n    contains: [\n      hljs.COMMENT(\n        '///',\n        '$',\n        {\n          returnBegin: true,\n          contains: [\n            {\n              className: 'doctag',\n              variants: [\n                {\n                  begin: '///',\n                  relevance: 0\n                },\n                {\n                  begin: '<!--|-->'\n                },\n                {\n                  begin: '</?',\n                  end: '>'\n                }\n              ]\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'if else elif endif define undef warning error line region endregion pragma checksum'\n        }\n      },\n      STRING,\n      NUMBERS,\n      {\n        beginKeywords: 'class interface',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:,]/,\n        contains: [\n          {\n            beginKeywords: \"where class\"\n          },\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'record',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        // [Attributes(\"\")]\n        className: 'meta',\n        begin: '^\\\\s*\\\\[',\n        excludeBegin: true,\n        end: '\\\\]',\n        excludeEnd: true,\n        contains: [\n          {\n            className: 'meta-string',\n            begin: /\"/,\n            end: /\"/\n          }\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new return throw await else',\n        relevance: 0\n      },\n      {\n        className: 'function',\n        begin: '(' + TYPE_IDENT_RE + '\\\\s+)+' + hljs.IDENT_RE + '\\\\s*(<.+>\\\\s*)?\\\\(',\n        returnBegin: true,\n        end: /\\s*[{;=]/,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          // prevents these from being highlighted `title`\n          {\n            beginKeywords: FUNCTION_MODIFIERS.join(\" \"),\n            relevance: 0\n          },\n          {\n            begin: hljs.IDENT_RE + '\\\\s*(<.+>\\\\s*)?\\\\(',\n            returnBegin: true,\n            contains: [\n              hljs.TITLE_MODE,\n              GENERIC_MODIFIER\n            ],\n            relevance: 0\n          },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              STRING,\n              NUMBERS,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      AT_IDENTIFIER\n    ]\n  };\n}\n\nmodule.exports = csharp;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,MAAM;AACpB,YAAM,oBAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,qBAAqB;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,sBAAsB;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,SAAS,gBAAgB,OAAO,mBAAmB;AAAA,QACnD,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AACA,YAAM,aAAa,KAAK,QAAQ,KAAK,YAAY;AAAA,QAC/C,OAAO;AAAA,MACT,CAAC;AACD,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AACA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,wBAAwB,KAAK,QAAQ,iBAAiB;AAAA,QAC1D,SAAS;AAAA,MACX,CAAC;AACD,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,cAAc,KAAK,QAAQ,OAAO;AAAA,QACtC,SAAS;AAAA,MACX,CAAC;AACD,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,YAAM,+BAA+B;AAAA,QACnC,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,qCAAqC,KAAK,QAAQ,8BAA8B;AAAA,QACpF,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,MACP;AACA,kBAAY,WAAW;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA,KAAK,QAAQ,KAAK,sBAAsB;AAAA,UACtC,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,SAAS;AAAA,QACb,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAEA,YAAM,mBAAmB;AAAA,QACvB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,eAAe;AAAA,UACjB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,gBAAgB,KAAK,WAAW,OAAO,KAAK,WAAW,eAAe,KAAK,WAAW;AAC5F,YAAM,gBAAgB;AAAA;AAAA;AAAA,QAGpB,OAAO,MAAM,KAAK;AAAA,QAClB,WAAW;AAAA,MACb;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,aAAa;AAAA,cACb,UAAU;AAAA,gBACR;AAAA,kBACE,WAAW;AAAA,kBACX,UAAU;AAAA,oBACR;AAAA,sBACE,OAAO;AAAA,sBACP,WAAW;AAAA,oBACb;AAAA,oBACA;AAAA,sBACE,OAAO;AAAA,oBACT;AAAA,oBACA;AAAA,sBACE,OAAO;AAAA,sBACP,KAAK;AAAA,oBACP;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,WAAW;AAAA,YACX,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,cACjB;AAAA,cACA;AAAA,cACA;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,WAAW;AAAA,YACX,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,WAAW;AAAA,YACX,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,cAAc;AAAA,YACd,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,eAAe;AAAA,YACf,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,MAAM,gBAAgB,WAAW,KAAK,WAAW;AAAA,YACxD,aAAa;AAAA,YACb,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,UAAU;AAAA;AAAA,cAER;AAAA,gBACE,eAAe,mBAAmB,KAAK,GAAG;AAAA,gBAC1C,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,OAAO,KAAK,WAAW;AAAA,gBACvB,aAAa;AAAA,gBACb,UAAU;AAAA,kBACR,KAAK;AAAA,kBACL;AAAA,gBACF;AAAA,gBACA,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,kBACA;AAAA,kBACA,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}