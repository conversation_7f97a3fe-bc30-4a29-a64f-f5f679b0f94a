{"version": 3, "sources": ["../../highlight.js/lib/languages/sml.js"], "sourcesContent": ["/*\nLanguage: SML (Standard ML)\nAuthor: <PERSON> <<EMAIL>>\nDescription: SML language definition.\nWebsite: https://www.smlnj.org\nOrigin: ocaml.js\nCategory: functional\n*/\nfunction sml(hljs) {\n  return {\n    name: 'SML (Standard ML)',\n    aliases: [ 'ml' ],\n    keywords: {\n      $pattern: '[a-z_]\\\\w*!?',\n      keyword:\n        /* according to Definition of Standard ML 97  */\n        'abstype and andalso as case datatype do else end eqtype ' +\n        'exception fn fun functor handle if in include infix infixr ' +\n        'let local nonfix of op open orelse raise rec sharing sig ' +\n        'signature struct structure then type val with withtype where while',\n      built_in:\n        /* built-in types according to basis library */\n        'array bool char exn int list option order real ref string substring vector unit word',\n      literal:\n        'true false NONE SOME LESS EQUAL GREATER nil'\n    },\n    illegal: /\\/\\/|>>/,\n    contains: [\n      {\n        className: 'literal',\n        begin: /\\[(\\|\\|)?\\]|\\(\\)/,\n        relevance: 0\n      },\n      hljs.COMMENT(\n        '\\\\(\\\\*',\n        '\\\\*\\\\)',\n        {\n          contains: [ 'self' ]\n        }\n      ),\n      { /* type variable */\n        className: 'symbol',\n        begin: '\\'[A-Za-z_](?!\\')[\\\\w\\']*'\n        /* the grammar is ambiguous on how 'a'b should be interpreted but not the compiler */\n      },\n      { /* polymorphic variant */\n        className: 'type',\n        begin: '`[A-Z][\\\\w\\']*'\n      },\n      { /* module or constructor */\n        className: 'type',\n        begin: '\\\\b[A-Z][\\\\w\\']*',\n        relevance: 0\n      },\n      { /* don't color identifiers, but safely catch all identifiers with ' */\n        begin: '[a-z_]\\\\w*\\'[\\\\w\\']*'\n      },\n      hljs.inherit(hljs.APOS_STRING_MODE, {\n        className: 'string',\n        relevance: 0\n      }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        illegal: null\n      }),\n      {\n        className: 'number',\n        begin:\n          '\\\\b(0[xX][a-fA-F0-9_]+[Lln]?|' +\n          '0[oO][0-7_]+[Lln]?|' +\n          '0[bB][01_]+[Lln]?|' +\n          '[0-9][0-9_]*([Lln]|(\\\\.[0-9_]*)?([eE][-+]?[0-9_]+)?)?)',\n        relevance: 0\n      },\n      {\n        begin: /[-=]>/ // relevance booster\n      }\n    ]\n  };\n}\n\nmodule.exports = sml;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,UAAU;AAAA,UACR,UAAU;AAAA,UACV;AAAA;AAAA,YAEE;AAAA;AAAA,UAIF;AAAA;AAAA,YAEE;AAAA;AAAA,UACF,SACE;AAAA,QACJ;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,UAAU,CAAE,MAAO;AAAA,YACrB;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA;AAAA,UAET;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA,KAAK,QAAQ,KAAK,kBAAkB;AAAA,YAClC,WAAW;AAAA,YACX,WAAW;AAAA,UACb,CAAC;AAAA,UACD,KAAK,QAAQ,KAAK,mBAAmB;AAAA,YACnC,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,WAAW;AAAA,YACX,OACE;AAAA,YAIF,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}