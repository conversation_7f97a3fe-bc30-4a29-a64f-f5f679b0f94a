{"version": 3, "sources": ["../../refractor/lang/ichigojam.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = ichigojam\nichigojam.displayName = 'ichigojam'\nichigojam.aliases = []\nfunction ichigojam(Prism) {\n  // according to the offical reference (EN)\n  // https://ichigojam.net/IchigoJam-en.html\n  Prism.languages.ichigojam = {\n    comment: /(?:\\B'|REM)(?:[^\\n\\r]*)/i,\n    string: {\n      pattern: /\"(?:\"\"|[!#$%&'()*,\\/:;<=>?^\\w +\\-.])*\"/,\n      greedy: true\n    },\n    number: /\\B#[0-9A-F]+|\\B`[01]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:E[+-]?\\d+)?/i,\n    keyword:\n      /\\b(?:BEEP|BPS|CASE|CLEAR|CLK|CLO|CLP|CLS|CLT|CLV|CONT|COPY|ELSE|END|FILE|FILES|FOR|GOSUB|GOTO|GSB|IF|INPUT|KBD|LED|LET|LIST|LOAD|LOCATE|LRUN|NEW|NEXT|OUT|PLAY|POKE|PRINT|PWM|REM|RENUM|RESET|RETURN|RIGHT|RTN|RUN|SAVE|SCROLL|SLEEP|SRND|STEP|STOP|SUB|TEMPO|THEN|TO|UART|VIDEO|WAIT)(?:\\$|\\b)/i,\n    function:\n      /\\b(?:ABS|ANA|ASC|BIN|BTN|DEC|END|FREE|HELP|HEX|I2CR|I2CW|IN|INKEY|LEN|LINE|PEEK|RND|SCR|SOUND|STR|TICK|USR|VER|VPEEK|ZER)(?:\\$|\\b)/i,\n    label: /(?:\\B@\\S+)/,\n    operator: /<[=>]?|>=?|\\|\\||&&|[+\\-*\\/=|&^~!]|\\b(?:AND|NOT|OR)\\b/i,\n    punctuation: /[\\[,;:()\\]]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,cAAU,cAAc;AACxB,cAAU,UAAU,CAAC;AACrB,aAAS,UAAU,OAAO;AAGxB,YAAM,UAAU,YAAY;AAAA,QAC1B,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,QACR,SACE;AAAA,QACF,UACE;AAAA,QACF,OAAO;AAAA,QACP,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}