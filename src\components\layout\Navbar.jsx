import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Menu, X, Code2 } from 'lucide-react'
import clsx from 'clsx'

function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const location = useLocation()

  const navigation = [
    { name: 'Home', href: '/', icon: '🏠' },
    { name: 'Blog', href: '/blog', icon: '📝' },
    { name: 'Projects', href: '/projects', icon: '🚀' },
    { name: 'Devlog', href: '/devlog', icon: '📜' },
    { name: 'About', href: '/about', icon: '👾' },
    { name: 'Contact', href: '/contact', icon: '📬' },
  ]

  const isActive = (href) => {
    if (href === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(href)
  }

  return (
    <nav className="fixed top-0 w-full z-50 bg-dark-bg/90 backdrop-blur-md border-b border-dark-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 group">
            <Code2 className="h-8 w-8 text-neon-blue group-hover:animate-glow" />
            <span className="text-xl font-bold text-gradient glow-text">
              BLUEPRINT
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={clsx(
                  'flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-300',
                  isActive(item.href)
                    ? 'text-neon-blue bg-neon-blue/10 border border-neon-blue/30'
                    : 'text-dark-text-secondary hover:text-neon-blue hover:bg-neon-blue/5'
                )}
              >
                <span>{item.icon}</span>
                <span>{item.name}</span>
              </Link>
            ))}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-dark-text-secondary hover:text-neon-blue p-2"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden bg-dark-card border-t border-dark-border">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                onClick={() => setIsOpen(false)}
                className={clsx(
                  'flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium transition-all duration-300',
                  isActive(item.href)
                    ? 'text-neon-blue bg-neon-blue/10 border border-neon-blue/30'
                    : 'text-dark-text-secondary hover:text-neon-blue hover:bg-neon-blue/5'
                )}
              >
                <span>{item.icon}</span>
                <span>{item.name}</span>
              </Link>
            ))}
          </div>
        </div>
      )}
    </nav>
  )
}

export default Navbar
