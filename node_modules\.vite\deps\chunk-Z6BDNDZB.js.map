{"version": 3, "sources": ["../../refractor/lang/lolcode.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = lolcode\nlolcode.displayName = 'lolcode'\nlolcode.aliases = []\nfunction lolcode(Prism) {\n  Prism.languages.lolcode = {\n    comment: [/\\bOBTW\\s[\\s\\S]*?\\sTLDR\\b/, /\\bBTW.+/],\n    string: {\n      pattern: /\"(?::.|[^\":])*\"/,\n      inside: {\n        variable: /:\\{[^}]+\\}/,\n        symbol: [/:\\([a-f\\d]+\\)/i, /:\\[[^\\]]+\\]/, /:[)>o\":]/]\n      },\n      greedy: true\n    },\n    number: /(?:\\B-)?(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)/,\n    symbol: {\n      pattern: /(^|\\s)(?:A )?(?:BUKKIT|NOOB|NUMBAR|NUMBR|TROOF|YARN)(?=\\s|,|$)/,\n      lookbehind: true,\n      inside: {\n        keyword: /A(?=\\s)/\n      }\n    },\n    label: {\n      pattern: /((?:^|\\s)(?:IM IN YR|IM OUTTA YR) )[a-zA-Z]\\w*/,\n      lookbehind: true,\n      alias: 'string'\n    },\n    function: {\n      pattern: /((?:^|\\s)(?:HOW IZ I|I IZ|IZ) )[a-zA-Z]\\w*/,\n      lookbehind: true\n    },\n    keyword: [\n      {\n        pattern:\n          /(^|\\s)(?:AN|FOUND YR|GIMMEH|GTFO|HAI|HAS A|HOW IZ I|I HAS A|I IZ|IF U SAY SO|IM IN YR|IM OUTTA YR|IS NOW(?: A)?|ITZ(?: A)?|IZ|KTHX|KTHXBYE|LIEK(?: A)?|MAEK|MEBBE|MKAY|NERFIN|NO WAI|O HAI IM|O RLY\\?|OIC|OMG|OMGWTF|R|SMOOSH|SRS|TIL|UPPIN|VISIBLE|WILE|WTF\\?|YA RLY|YR)(?=\\s|,|$)/,\n        lookbehind: true\n      },\n      /'Z(?=\\s|,|$)/\n    ],\n    boolean: {\n      pattern: /(^|\\s)(?:FAIL|WIN)(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    variable: {\n      pattern: /(^|\\s)IT(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    operator: {\n      pattern:\n        /(^|\\s)(?:NOT|BOTH SAEM|DIFFRINT|(?:ALL|ANY|BIGGR|BOTH|DIFF|EITHER|MOD|PRODUKT|QUOSHUNT|SMALLR|SUM|WON) OF)(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    punctuation: /\\.{3}|…|,|!/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AACtB,YAAM,UAAU,UAAU;AAAA,QACxB,SAAS,CAAC,4BAA4B,SAAS;AAAA,QAC/C,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,UAAU;AAAA,YACV,QAAQ,CAAC,kBAAkB,eAAe,UAAU;AAAA,UACtD;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,SAAS;AAAA,UACP;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,UACR,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}